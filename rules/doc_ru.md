# RST Document Review Rules

## Structure and Format Rules

1. **Title Structure**: Document should have a clear title hierarchy using proper RST title markers (=, -, ^, ", ', etc.)
2. **Section Headers**: Each section should have appropriate underline length matching the title text
3. **Code Blocks**: Code blocks should use proper syntax highlighting with language specification
4. **Lists**: Use consistent list formatting (-, *, +) and proper indentation
5. **Links**: External links should be properly formatted and functional
6. **References**: Internal references should use proper RST syntax (:ref:, :doc:, etc.)

## Content Quality Rules

7. **Grammar**: Check for basic grammar errors and sentence structure
8. **Spelling**: Identify potential spelling mistakes
9. **Consistency**: Terminology should be consistent throughout the document
10. **Clarity**: Sentences should be clear and concise
11. **Technical Accuracy**: Technical terms should be used correctly

## RST Specific Rules

12. **Directive Syntax**: RST directives should follow proper syntax (.. directive::)
13. **Cross-references**: References to other sections should be valid
14. **Table Format**: Tables should be properly formatted with consistent column alignment
15. **Image References**: Image directives should have proper paths and alt text
16. **Indentation**: Proper indentation for nested content (code blocks, lists, etc.)

## Documentation Standards

17. **Introduction**: Document should have a clear introduction explaining its purpose
18. **Examples**: Include relevant examples where appropriate
19. **Completeness**: All sections should be complete and not contain TODO or placeholder text
20. **Readability**: Document should flow logically from section to section
