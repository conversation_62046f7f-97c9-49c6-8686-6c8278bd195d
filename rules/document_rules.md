# RST Document Review Rules

## Structure and Format Rules

1. 标题层级是否正常,不能存在跳级,不能存在鼓励的标题,上下级标题之间的内容不能重复
2. 可读性:语言是否简洁易懂,句子不能太长,不要使用多重否定句,避免使用复合句,同一个意思,尽量用肯定句表达
3. 段落只能有一个主题,字间距正常,不能全角和半角混用,英语的大小写要符合规范
4. 感叹号不要连用,中文得用全角符号,英文用英文标点;
5. 格式规范:标题、代码块等格式是否统一
6. 拼写错误
7. 语法和表达问题
8. 格式和标点问题
9. 技术术语一致性
10. 代码和注释遵循TMG规范
11. 提示文本不建议太长,内容简洁
12. 引用注明出处
13. 缩略语使用正确,需有对应的解释
14. 描述里提到的专有名称元素（文件名，目录，命令，宏）都用反引号括起来， 象这样``make lunch``