// Sample C file to test the Show Caller feature

#include <stdio.h>

// Target function that will be called by others
void target_function() {
    printf("This is the target function\n");
}

// Parent function 1 that calls target_function
void parent_function_1() {
    printf("Parent function 1\n");
    target_function();  // Line 12
    printf("After calling target\n");
}

// Parent function 2 that calls target_function
void parent_function_2() {
    printf("Parent function 2\n");
    target_function();  // Line 19
    if (1) {
        target_function();  // Line 21
    }
}

// Parent function 3 that calls target_function
void parent_function_3() {
    printf("Parent function 3\n");
    target_function();  // Line 27
}

// Main function that calls parent functions
int main() {
    parent_function_1();
    parent_function_2();
    parent_function_3();
    return 0;
}
