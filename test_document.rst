Test Document
=============

This is a test RST document with some intentional issues for testing the document review feature.

Section 1
---------

This section has some problems:
- Missing proper introduction
- Code block without language specification::

    def hello():
        print("Hello World")

Section 2
=========

This section has inconsistent title formatting (should use - not =).

Here's a list with inconsistent formatting:
* Item 1
- Item 2
+ Item 3

.. note::
   This is a note directive that should work fine.

Conclusion
----------

This document contains several issues that the AI should detect:
1. Inconsistent title formatting
2. Missing language specification in code blocks
3. Inconsistent list formatting
4. Missing proper introduction section
