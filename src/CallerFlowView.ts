import * as vscode from 'vscode';
import * as path from 'path';
import { findCallers, SymbolInfo } from './CallHierarchyProvider';
import { findFunctionNameByLine, outputChannel, getFullPath } from './UtilFuns';

interface CallerNode {
    name: string;
    filePath: string;
    lineNumbers: number[];
    description: string;
}

export function registerShowCallerCommand(context: vscode.ExtensionContext): void {
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.showCaller', async () => {
            try {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showErrorMessage('No active editor found');
                    return;
                }

                // Get the function name under the cursor
                const position = editor.selection.active;
                const document = editor.document;
                const range = document.getWordRangeAtPosition(position);

                if (!range) {
                    vscode.window.showErrorMessage('No word found under cursor');
                    return;
                }

                const functionName = document.getText(range);

                // Check if current file is C/C++
                const fileName = document.fileName.toLowerCase();
                const isCpp = fileName.endsWith('.cpp') || fileName.endsWith('.cc') ||
                    fileName.endsWith('.cxx') || fileName.endsWith('.hpp') ||
                    fileName.endsWith('.h++');
                const isC = fileName.endsWith('.c') || fileName.endsWith('.h');

                if (!isCpp && !isC) {
                    vscode.window.showErrorMessage('Show Caller is only supported for C/C++ files');
                    return;
                }

                // Show progress
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `Finding callers for ${functionName}...`,
                    cancellable: false
                }, async (progress) => {
                    try {
                        // Find all callers
                        const callers = await findCallers(functionName, isCpp);

                        if (callers.length === 0) {
                            vscode.window.showInformationMessage(`No callers found for function: ${functionName}`);
                            return;
                        }

                        // Group callers by function name and collect line numbers
                        const callerMap = new Map<string, CallerNode>();

                        for (const caller of callers) {
                            const key = `${caller.name}:${caller.filePath}`;
                            if (callerMap.has(key)) {
                                const existing = callerMap.get(key)!;
                                existing.lineNumbers.push(caller.linePosition);
                            } else {
                                callerMap.set(key, {
                                    name: caller.name,
                                    filePath: caller.filePath,
                                    lineNumbers: [caller.linePosition],
                                    description: caller.description || ''
                                });
                            }
                        }

                        // Create and show the webview
                        const panel = vscode.window.createWebviewPanel(
                            'callerFlow',
                            `Caller Flow: ${functionName}`,
                            vscode.ViewColumn.One,
                            {
                                enableScripts: true,
                                retainContextWhenHidden: true
                            }
                        );

                        // Set the HTML content
                        panel.webview.html = getWebviewContent(functionName, Array.from(callerMap.values()), isCpp);

                        // Handle messages from the webview
                        panel.webview.onDidReceiveMessage(
                            async (message) => {
                                switch (message.command) {
                                    case 'openFile':
                                        try {
                                            // Resolve the full path
                                            const fullPath = getFullPath(message.filePath);
                                            const uri = vscode.Uri.file(fullPath);
                                            const doc = await vscode.workspace.openTextDocument(uri);
                                            // Open in right panel (ViewColumn.Two)
                                            const editor = await vscode.window.showTextDocument(doc, vscode.ViewColumn.Two);

                                            // Jump to the specific line
                                            const line = message.lineNumber - 1; // Convert to 0-based
                                            const position = new vscode.Position(line, 0);
                                            editor.selection = new vscode.Selection(position, position);
                                            editor.revealRange(new vscode.Range(position, position));
                                        } catch (error) {
                                            vscode.window.showErrorMessage(`Failed to open file: ${error}`);
                                        }
                                        break;
                                    case 'expandFunction':
                                        try {
                                            // Find callers for the clicked function
                                            const expandCallers = await findCallers(message.functionName, isCpp);
                                            const expandCallerMap = new Map<string, CallerNode>();

                                            for (const caller of expandCallers) {
                                                const key = `${caller.name}:${caller.filePath}`;
                                                if (expandCallerMap.has(key)) {
                                                    const existing = expandCallerMap.get(key)!;
                                                    existing.lineNumbers.push(caller.linePosition);
                                                } else {
                                                    expandCallerMap.set(key, {
                                                        name: caller.name,
                                                        filePath: caller.filePath,
                                                        lineNumbers: [caller.linePosition],
                                                        description: caller.description || ''
                                                    });
                                                }
                                            }

                                            // Send the expanded data back to webview
                                            panel.webview.postMessage({
                                                command: 'updateExpanded',
                                                functionName: message.functionName,
                                                callers: Array.from(expandCallerMap.values())
                                            });
                                        } catch (error) {
                                            vscode.window.showErrorMessage(`Failed to expand function: ${error}`);
                                        }
                                        break;
                                }
                            },
                            undefined,
                            context.subscriptions
                        );

                            // Handle copy call flow request from webview
                            panel.webview.onDidReceiveMessage(
                                async (message) => {
                                    if (message.command === 'copyCallFlow') {
                                        try {
                                            await vscode.env.clipboard.writeText(message.text || '');
                                            vscode.window.showInformationMessage(`Copied CallFlow: ${message.text}`);
                                        } catch (err) {
                                            vscode.window.showErrorMessage(`Failed to copy CallFlow: ${err}`);
                                        }
                                    }
                                },
                                undefined,
                                context.subscriptions
                            );

                    } catch (error) {
                        outputChannel.appendLine(`Error finding callers: ${error}`);
                        vscode.window.showErrorMessage(`Error finding callers: ${error}`);
                    }
                });

            } catch (error) {
                outputChannel.appendLine(`Error in showCaller command: ${error}`);
                vscode.window.showErrorMessage(`Error: ${error}`);
            }
        })
    );
}

function getWebviewContent(childFunction: string, callers: CallerNode[], isCpp: boolean): string {
    const callerElements = callers.map((caller, index) => {
        const lineNumbers = caller.lineNumbers.sort((a, b) => a - b);
        return `
            <div class="tree-item" data-level="1">
                <div class="tree-line"></div>
                <div class="caller-node" id="caller-${index}" onclick="expandFunction('${escapeHtml(caller.name)}')">
                    <div class="function-name">${escapeHtml(caller.name)}</div>
                    <div class="line-numbers">
                        ${lineNumbers.map(line =>
                            `<span class="line-number" onclick="event.stopPropagation(); openFile('${escapeHtml(caller.filePath)}', ${line})">line +${line}</span>`
                        ).join('<br>')}
                    </div>
                    <div class="expand-indicator">▶</div>
                </div>
                <div class="expanded-content" id="expanded-${index}" style="display: none;"></div>
            </div>
        `;
    }).join('');

    // Helper function to escape HTML
    function escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Caller Flow</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 20px;
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
            }
            .container {
                display: flex;
                flex-direction: column;
                gap: 20px;
                max-width: 800px;
                margin: 0 auto;
            }
            .title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: left;
            }
            .flow-container {
                display: flex;
                flex-direction: column;
                gap: 0;
                width: 100%;
            }
            .tree-item {
                position: relative;
                margin-left: 20px;
            }
            .tree-line {
                position: absolute;
                left: -20px;
                top: 0;
                bottom: 0;
                width: 2px;
                background-color: var(--vscode-input-border);
            }
            .tree-line::before {
                content: '';
                position: absolute;
                top: 25px;
                left: 0;
                width: 20px;
                height: 2px;
                background-color: var(--vscode-input-border);
            }
            .caller-node {
                background-color: var(--vscode-input-background);
                border: 2px solid var(--vscode-input-border);
                border-radius: 6px;
                padding: 8px 10px;
                min-width: 120px;
                max-width: 220px;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
                position: relative;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            .caller-node:hover {
                border-color: var(--vscode-focusBorder);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                transform: translateX(2px);
            }
            .function-name {
                font-size: 16px;
                font-weight: bold;
                color: var(--vscode-symbolIcon-functionForeground);
            }
            .expand-indicator {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
                transition: transform 0.2s ease;
            }
            .caller-node.expanded .expand-indicator {
                transform: translateY(-50%) rotate(90deg);
            }
            .line-numbers {
                display: block;
                font-size: 12px;
            }
            .line-number {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 2px 6px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
                transition: all 0.2s ease;
                font-weight: 500;
                display: inline-block;
                margin-bottom: 2px;
            }
            .line-number:hover {
                background-color: var(--vscode-button-hoverBackground);
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .expanded-content {
                margin-left: 20px;
                margin-top: 10px;
            }
            .expanded-content .tree-item {
                margin-left: 0;
            }
            .child-function {
                background-color: var(--vscode-editor-selectionBackground);
                border: 3px solid var(--vscode-focusBorder);
                border-radius: 6px;
                padding: 16px 20px;
                min-width: 200px;
                font-size: 18px;
                font-weight: bold;
                color: var(--vscode-symbolIcon-functionForeground);
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                margin-bottom: 20px;
            }
            .no-callers {
                color: var(--vscode-descriptionForeground);
                font-style: italic;
                margin: 40px 0;
                text-align: center;
            }
            /* Context menu styles */
            .context-menu {
                position: fixed;
                background-color: var(--vscode-editor-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                min-width: 160px;
                overflow: hidden;
            }
            .context-menu .menu-item {
                padding: 6px 10px;
                font-size: 13px;
                cursor: pointer;
                color: var(--vscode-foreground);
            }
            .context-menu .menu-item:hover {
                background-color: var(--vscode-list-hoverBackground);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="title">Caller Flow: ${escapeHtml(childFunction)}</div>

            <div class="child-function" id="target-function">
                ${escapeHtml(childFunction)}
                <div style="font-size: 12px; font-weight: normal; margin-top: 4px; color: var(--vscode-descriptionForeground);">
                    (Target Function)
                </div>
            </div>

            <div class="flow-container">
                ${callers.length > 0 ? callerElements : '<div class="no-callers">No callers found</div>'}
            </div>
        </div>

            <!-- Custom context menu -->
            <div id="context-menu" class="context-menu" style="display:none">
                <div class="menu-item" id="copy-callflow">Copy CallFlow</div>
            </div>

        <script>
            const TARGET_FUNCTION = ${JSON.stringify(childFunction)};
            const vscode = acquireVsCodeApi();

            function openFile(filePath, lineNumber) {
                vscode.postMessage({
                    command: 'openFile',
                    filePath: filePath,
                    lineNumber: lineNumber
                });
            }

            function expandFunction(functionName) {
                vscode.postMessage({
                    command: 'expandFunction',
                    functionName: functionName
                });
            }

            // Handle messages from extension
            window.addEventListener('message', event => {
                const message = event.data;

            // Build call flow path string from target to clicked node
            function buildCallFlowString(clickedNode) {
                const names = [];
                // Start from clicked node's function name
                let node = clickedNode;
                while (node) {
                    const nameEl = node.querySelector('.function-name');
                    if (nameEl) {
                        names.unshift(nameEl.textContent);
                    }
                    // Move up to the parent caller-node, if exists
                    const parentTreeItem = node.closest('.tree-item');
                    if (!parentTreeItem) break;
                    const parentOfTreeItem = parentTreeItem.parentElement.closest('.tree-item');
                    if (!parentOfTreeItem) break;
                    node = parentOfTreeItem.querySelector(':scope > .caller-node');
                }
                // Prepend target function at the beginning
                names.unshift(TARGET_FUNCTION);
                // Deduplicate consecutive duplicates just in case
                const dedup = names.filter((n, i) => i === 0 || n !== names[i-1]);
                return dedup.join(' <= ');
            }

            // Context menu logic
            const contextMenu = document.getElementById('context-menu');
            const copyItem = document.getElementById('copy-callflow');
            let currentRightClickedNode = null;

            document.addEventListener('contextmenu', (e) => {
                const node = e.target.closest('.caller-node');
                if (node) {
                    e.preventDefault();
                    currentRightClickedNode = node;
                    contextMenu.style.display = 'block';
                    contextMenu.style.left = e.pageX + 'px';
                    contextMenu.style.top = e.pageY + 'px';
                } else {
                    // Hide if right-clicked elsewhere
                    contextMenu.style.display = 'none';
                }
            });

            document.addEventListener('click', () => {
                contextMenu.style.display = 'none';
            });

            copyItem.addEventListener('click', (e) => {
                e.stopPropagation();
                if (currentRightClickedNode) {
                    const text = buildCallFlowString(currentRightClickedNode);
                    vscode.postMessage({ command: 'copyCallFlow', text });
                }
                contextMenu.style.display = 'none';
            });
                switch (message.command) {
                    case 'updateExpanded':
                        updateExpandedContent(message.functionName, message.callers);
                        break;
                }
            });

            function updateExpandedContent(functionName, callers) {
                // Find the caller node for this function
                const callerNodes = document.querySelectorAll('.caller-node');
                let targetNode = null;

                for (const node of callerNodes) {
                    const nameElement = node.querySelector('.function-name');
                    if (nameElement && nameElement.textContent === functionName) {
                        targetNode = node;
                        break;
                    }
                }

                if (targetNode) {
                    const expandedContent = targetNode.parentElement.querySelector('.expanded-content');
                    const expandIndicator = targetNode.querySelector('.expand-indicator');

                    if (expandedContent.style.display === 'none') {
                        // Expand
                        if (callers.length > 0) {
                            const expandedHtml = callers.map((caller, index) => {
                                const lineNumbers = caller.lineNumbers.sort((a, b) => a - b);
                                return \`
                                    <div class="tree-item" data-level="2">
                                        <div class="tree-line"></div>
                                        <div class="caller-node" onclick="expandFunction('\${caller.name}')">
                                            <div class="function-name">\${caller.name}</div>
                                            <div class="line-numbers">
                                                \${lineNumbers.map(line =>
                                                    \`<span class="line-number" onclick="event.stopPropagation(); openFile('\${caller.filePath}', \${line})">+\${line}</span>\`
                                                ).join(' ')}
                                            </div>
                                            <div class="expand-indicator">▶</div>
                                        </div>
                                        <div class="expanded-content" style="display: none;"></div>
                                    </div>
                                \`;
                            }).join('');
                            expandedContent.innerHTML = expandedHtml;
                        } else {
                            expandedContent.innerHTML = '<div class="no-callers">No callers found</div>';
                        }
                        expandedContent.style.display = 'block';
                        expandIndicator.style.transform = 'translateY(-50%) rotate(90deg)';
                        targetNode.classList.add('expanded');
                    } else {
                        // Collapse
                        expandedContent.style.display = 'none';
                        expandIndicator.style.transform = 'translateY(-50%) rotate(0deg)';
                        targetNode.classList.remove('expanded');
                    }
                }
            }
        </script>
    </body>
    </html>`;
}
