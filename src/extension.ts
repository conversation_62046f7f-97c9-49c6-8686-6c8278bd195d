import * as vscode from 'vscode';
import { registerCscopeMoreCommand } from './CscopeFind';
//import { FunctionTreeProvider } from './functionTreeProvider';

const { exec } = require('child_process');
const { promisify } = require('util');

const { ReadtagsProvider } = require("./ReadTags");
import { EXTENSION_ID } from './globalSettings';
const { getConfiguration, commandGuard, wrapExec } = require("./UtilFuns");

import { EXTENSION_NAME, TASK_NAME, ctagsGenerateCommand } from './globalSettings';

const callHierarchyProvider = require("./CallHierarchyProvider");
const fs = require('fs');
import {RefProvider} from './RefProvider';
import path = require('path');
import { g_browserHistoryViewProvider, registerBrowserHistoryView } from './BrowserHistoryView';
import BrowserHistory from './BrowserHistoryStore';
import { registerSymbolPreviewView, saveSymbolPreviewSettings } from './SymbolPreviewView';
import { FunctionPointerMap, registerFunctionPointerMap } from './FunctionPointerMap';
import { registerEditWindowCommand } from './EditWindow';
import { registerReviewCodeCommand } from './ReviewCode';
import { registerTranslateLangCommands } from './TranslateLang';

import { outputChannel } from './UtilFuns';
import { registerAboutCommand } from './About';
import { registerCollectCscopeFilesCommand } from './CollectCscopeFiles';
import { registerExportCallHierarchyCommand } from './ExportCallHierarchy';
import { registerMacroDefinitionCommand } from './MacroDefinition';
import { registerCommentToggleCommand } from './CommentToggle';
import { registerCoverityViolationCommand } from './CoverityViolation';
import { registerAIModelSelector } from './AIModelSelector';
import { registerShowCallerCommand } from './CallerFlowView';
import { registerQuickAICommand } from './QuickAI';

let status : vscode.StatusBarItem | undefined;
let { g_context } = require('./globalSettings');

export let g_browserHistory: BrowserHistory;
export let g_functionPointerMap: FunctionPointerMap;

function updateStatus(text:string) {
    if (status) {
        status.text = text;

        if (text) {
            status.show();
        } else {
            status.hide();
        }
    }
}


async function initializeSubscriptions(extensionContext: vscode.ExtensionContext): Promise<void> {
    await vscode.commands.executeCommand('setContext', 'enableCommands', true);
    const enableCallHierarchy = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableCallHierarchy") as boolean;

    if (!enableCallHierarchy) {
        vscode.window.showInformationMessage("C/C++ Call Hierarchy is disabled. ");
        return;
    }

    const my_callHierarchyProvider = new callHierarchyProvider.CCallHierarchyProvider(extensionContext);
    const commands = await vscode.commands.getCommands(true);

    outputChannel.appendLine("Register call hierarchy commands");

    extensionContext.subscriptions.push(
        !commands.includes('sourceseek.build') ?
            vscode.commands.registerCommand('sourceseek.build', async () =>
                await my_callHierarchyProvider.buildDatabase(callHierarchyProvider.DatabaseType.BOTH)) :
            new vscode.Disposable(() => undefined),
        );

    extensionContext.subscriptions.push(
        !commands.includes('.showIncludeHierarchy') ?
            vscode.commands.registerCommand('sourceseek.showIncludeHierarchy', async () =>
                await vscode.commands.executeCommand('references-view.showCallHierarchy')) :
            new vscode.Disposable(() => undefined),

        vscode.languages.registerCallHierarchyProvider({
            scheme: 'file',
            language: 'c'
        }, my_callHierarchyProvider),

        vscode.languages.registerCallHierarchyProvider({
            scheme: 'file',
            language: 'cpp'
        }, my_callHierarchyProvider)
    );
}

async function initializeReadtags(context: vscode.ExtensionContext ) {
    console.time("[SourceSeek] activate");
    const documentSelector = getConfiguration().get("documentSelector") as vscode.DocumentSelector;

    if (vscode.workspace.workspaceFolders) {
        vscode.workspace.workspaceFolders.forEach(scope =>
            context.subscriptions.push(
                vscode.tasks.registerTaskProvider("shell", {
                    provideTasks: () => {
                        const command = ctagsGenerateCommand
                        if (commandGuard(command)) return [];
                        const task = new vscode.Task(
                            { type: "shell" },
                            scope,
                            TASK_NAME,
                            EXTENSION_NAME,
                            new vscode.ShellExecution(command),
                            []
                        );
                        task.presentationOptions.reveal = undefined;
                        return [task];
                    },
                    resolveTask: (task) => task
                })
            ));
    }

    const provider = new ReadtagsProvider(context,wrapExec(promisify(exec)));
    const enableDefinitionSearch = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableDefinitionSearch") as boolean;
    if (enableDefinitionSearch) {
        outputChannel.appendLine("Definition Search is enabled. ");
        context.subscriptions.push(vscode.languages.registerDefinitionProvider(documentSelector, provider));
    }

    const enableWorkspaceSymbol = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableWorkspaceSymbol") as boolean;
    if (enableWorkspaceSymbol) {
        outputChannel.appendLine("Workspace Symbol is enabled. ");
        vscode.window.showInformationMessage("Workspace Symbol is enabled. ");
        context.subscriptions.push(vscode.languages.registerWorkspaceSymbolProvider(provider));
    }
    const enableDocumentSymbol = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableDocumentSymbol") as boolean;
    if (enableDocumentSymbol) {
        outputChannel.appendLine("Document Symbol is enabled. ");
        context.subscriptions.push(vscode.languages.registerDocumentSymbolProvider(
            documentSelector,
            provider,
            { label: EXTENSION_NAME }
        ));
    }
    // Register command to toggle function-only filter

    console.timeEnd("[SourceSeek] activate");
}

function initializeCscope(context: vscode.ExtensionContext) {
    let enableScope = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableReferenceSearch") as boolean;
    if (!enableScope) {
        vscode.window.showInformationMessage("Reference Search is disabled. ");
        return;
    }


    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
        const workspaceFolder = vscode.workspace.workspaceFolders[0].uri.fsPath;
        status = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        context.subscriptions.push(status);

        const cscopePath = path.join(context.extensionPath, 'cscope.exe');

        try{
            fs.accessSync(path.join(workspaceFolder, ".vscode"), fs.constants.R_OK | fs.constants.W_OK)
        }
        catch{
            vscode.window.showInformationMessage("Note: .vscode folder does not exist, creating new one");
            fs.mkdirSync(path.join(workspaceFolder, ".vscode"));
        }
        const database_path = path.join(workspaceFolder, ".vscode");

        try{
            fs.accessSync(database_path, fs.constants.R_OK | fs.constants.W_OK)
        }
        catch{
            vscode.window.showErrorMessage("cscope database folder does not exist, creating new one");
            fs.mkdirSync(database_path);
        }
        context.subscriptions.push(vscode.languages.registerReferenceProvider(["cpp", "c"], new RefProvider()));
    }
}

function internalRegister(context: vscode.ExtensionContext) {

    // Register the review code command
    registerReviewCodeCommand(context);

    // Register the AI model selector
    registerAIModelSelector(context);

    // Register translation commands
    registerTranslateLangCommands(context);

    // Register the quick AI command (Ctrl+K)
    registerQuickAICommand(context);

    // Register the coverity violation command
    registerCoverityViolationCommand(context);
}
/**
 * Execute a shell command and return the output
 * @param command The command to execute
 * @param cwd The working directory
 * @returns The command output
 */
export function activate(context: vscode.ExtensionContext) {
    g_context = context;
    const enableSourceSeek = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("enableSourceSeek") as boolean;

    if (!enableSourceSeek) {
        vscode.window.showInformationMessage("SourceSeek is disabled. Enable it in settings to use its features.");
        return;
    }

    initializeSubscriptions(context);
    initializeReadtags(context);
    //initializeFunctionTreeView(context);
    initializeCscope(context);

    // Get the maximum history size from settings (default to 100 if not specified)
    const maxHistorySize = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("maxHistorySize") as number || 100;
    g_browserHistory = new BrowserHistory(maxHistorySize, context);
    try {
            g_browserHistory.load().then(()=>{
            registerBrowserHistoryView(context, g_browserHistory);
            g_browserHistoryViewProvider?.refresh();
        });
    } catch (error) {
        // 如果是首次运行，文件不存在导致加载失败是正常的
        console.log('No existing history file found');
    }

    // 注册符号预览视图
    registerSymbolPreviewView(context);

    // 创建函数树视图提供者

    // Initialize the function pointer map
    g_functionPointerMap = new FunctionPointerMap();
    // Try to load existing mappings
    try {
        g_functionPointerMap.load(context);
    } catch (error) {
        console.log('No existing function pointer mappings found');
    }

    registerFunctionPointerMap(context);

    // Initialize the title at activation
    registerEditWindowCommand(context);


    // Register the collect cscope files command
    registerCollectCscopeFilesCommand(context);
    registerExportCallHierarchyCommand(context);

    // Register the macro definition command
    registerMacroDefinitionCommand(context);

    // Register the comment toggle command
    registerCommentToggleCommand(context);


    registerCscopeMoreCommand(context);
    // Register the show caller command
    registerShowCallerCommand(context);

    // Register the about command
    registerAboutCommand(context);

    internalRegister(context);

}


/*
function initializeFunctionTreeView(context: vscode.ExtensionContext) {
    const functionTreeProvider = new FunctionTreeProvider(context);


    // 注册树视图
    const treeView = vscode.window.createTreeView('functionTreeView', {
        treeDataProvider: functionTreeProvider,
        showCollapseAll: true
    });

    // 注册刷新命令
    context.subscriptions.push(
        vscode.commands.registerCommand('functionTreeView.refresh', () => {
            functionTreeProvider.refresh();
        })
    );

    // 注册跳转到函数命令
    context.subscriptions.push(
        vscode.commands.registerCommand('functionTreeView.gotoFunction', (filePath: string, range: vscode.Range) => {
            if (filePath) {
                vscode.workspace.openTextDocument(filePath).then(document => {
                    vscode.window.showTextDocument(document).then(editor => {
                        editor.revealRange(range, vscode.TextEditorRevealType.InCenter);
                        editor.selection = new vscode.Selection(range.start, range.start);
                    });
                });
            }
        })
    );

    // 当活动编辑器变化时刷新树视图
    context.subscriptions.push(
        vscode.window.onDidChangeActiveTextEditor(() => {
            functionTreeProvider.refresh();
        })
    );

    // 当文档保存时刷新树视图
    context.subscriptions.push(
        vscode.workspace.onDidSaveTextDocument(() => {
            functionTreeProvider.refresh();
        })
    );
}
*/
export async function deactivate() {
    // 保存设置
    saveSymbolPreviewSettings().catch(error => {
        console.error('保存设置时出错:', error);
    });

    // 在扩展停用时保存浏览历史记录
    if (g_browserHistory) {
        try {
            await g_browserHistory.save();
        } catch (error) {
            console.error('Failed to save browser history:', error);
        }
    }

    // Save function pointer mappings and dispose
    if (g_functionPointerMap) {
        try {
            g_functionPointerMap.dispose();
        } catch (error) {
            console.error('Failed to dispose function pointer map:', error);
        }
    }
}


async function refreshOutlineByForceSave() {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
        await editor.document.save();
    }
}

