import * as vscode from 'vscode';
import { getConfiguration } from './UtilFuns';

// Model mapping: display name -> actual model string
const MODEL_MAPPING = {
    'DeepSeek': 'DeepSeek-V3',
    'Qwen3-Coder': 'Qwen3-235B-A22B-Instruct-2507',
    'GLM-4.5': 'GLM-4.5',
    'Kimi': 'Kimi-K2-Instruct'
};

// Reverse mapping: model string -> display name
const REVERSE_MODEL_MAPPING: { [key: string]: string } = {};
for (const [displayName, modelString] of Object.entries(MODEL_MAPPING)) {
    REVERSE_MODEL_MAPPING[modelString] = displayName;
}

let aiModelStatusBarItem: vscode.StatusBarItem | undefined;

/**
 * Get the current AI model display name
 */
function getCurrentModelDisplayName(): string {
    const currentModel = getConfiguration().get("aiModel") as string || "DeepSeek-V3";
    return REVERSE_MODEL_MAPPING[currentModel] || 'DeepSeek';
}

/**
 * Update the status bar item text
 */
function updateStatusBarItem(): void {
    if (aiModelStatusBarItem) {
        const currentDisplayName = getCurrentModelDisplayName();
        aiModelStatusBarItem.text = `$(robot) ${currentDisplayName}`;
        aiModelStatusBarItem.tooltip = `Current AI Model: ${currentDisplayName}. Click to change.`;
    }
}

/**
 * Show the model selection menu
 */
async function showModelSelectionMenu(): Promise<void> {
    const currentModel = getConfiguration().get("aiModel") as string || "DeepSeek-V3";
    const currentDisplayName = REVERSE_MODEL_MAPPING[currentModel] || 'DeepSeek';
    
    const items: vscode.QuickPickItem[] = Object.keys(MODEL_MAPPING).map(displayName => ({
        label: displayName,
        description: MODEL_MAPPING[displayName as keyof typeof MODEL_MAPPING],
        picked: displayName === currentDisplayName
    }));

    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: 'Select AI Model',
        title: 'Choose AI Model for Code Review'
    });

    if (selected) {
        const selectedModelString = MODEL_MAPPING[selected.label as keyof typeof MODEL_MAPPING];
        
        // Update the configuration
        await getConfiguration().update("aiModel", selectedModelString, vscode.ConfigurationTarget.Global);
        
        // Update the status bar
        updateStatusBarItem();
        
        // Show confirmation message
        vscode.window.showInformationMessage(`AI Model changed to: ${selected.label}`);
    }
}

/**
 * Register the AI model selector functionality
 */
export function registerAIModelSelector(context: vscode.ExtensionContext): void {
    // Create status bar item
    aiModelStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
    aiModelStatusBarItem.command = 'sourceseek.selectAIModel';
    updateStatusBarItem();
    aiModelStatusBarItem.show();
    context.subscriptions.push(aiModelStatusBarItem);

    // Register command
    const disposable = vscode.commands.registerCommand('sourceseek.selectAIModel', showModelSelectionMenu);
    context.subscriptions.push(disposable);

    // Listen for configuration changes to update the status bar
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('sourceseek.aiModel')) {
            updateStatusBarItem();
        }
    });
    context.subscriptions.push(configChangeDisposable);
}
