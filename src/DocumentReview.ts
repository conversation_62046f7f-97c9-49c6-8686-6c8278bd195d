import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { requestAI, reviewOutputChannel } from './ReviewCode';

interface DocumentIssue {
    startLine: number;
    endLine: number;
    description: string;
    original: string;
    fixed: string;
}

interface DocumentReviewResponse {
    issues: DocumentIssue[];
}

let documentDecorations: vscode.TextEditorDecorationType[] = [];
let suggestionDecorations: vscode.TextEditorDecorationType[] = [];
let currentIssues: DocumentIssue[] = [];
let codeLensProvider: DocumentReviewCodeLensProvider | null = null;

// Decoration types for highlighting issues and suggestions
const issueDecorationType = vscode.window.createTextEditorDecorationType({
    backgroundColor: 'rgba(139, 0, 0, 0.66)', // Dark red background
    isWholeLine: true
});

const suggestionDecorationType = vscode.window.createTextEditorDecorationType({
    backgroundColor: 'rgba(0, 100, 0, 0.46)', // Dark green background
    isWholeLine: true
});

export async function reviewDocument(): Promise<void> {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const document = editor.document;
        if (!document.fileName.endsWith('.rst')) {
            vscode.window.showWarningMessage('This command is designed for RST documents');
        }

        // Clear previous decorations
        clearDecorations();

        const extensionPath = vscode.extensions.getExtension('lingxf.SourceSeek')?.extensionPath || '';
        const rulesPath = path.join(extensionPath, 'rules/document_rules.md');
        // Read document rules
        let rules = '';
        try {
            rules = fs.readFileSync(rulesPath, 'utf8');
        } catch (error) {
            vscode.window.showErrorMessage('Could not read document rules file');
            return;
        }

        const documentContent = document.getText();
        
        // Build prompt for AI
        const prompt = buildDocumentReviewPrompt(documentContent, rules);
        
        // Show progress
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "Reviewing document with AI...",
            cancellable: false
        }, async (progress) => {
            try {
                const start = Date.now();
                const aiResponse = await requestAI(prompt);
                if (!aiResponse) {
                    vscode.window.showInformationMessage('No response from AI');
                    return;
                }
                const secs = ((Date.now() - start) / 1000).toFixed(2);

                reviewOutputChannel.appendLine(`\n[AI request time cost: ${secs} seconds]`);
                reviewOutputChannel.appendLine(aiResponse);
                reviewOutputChannel.show(true);

                // Parse AI response
                const reviewResult = parseAIResponse(aiResponse);
                if (!reviewResult || reviewResult.issues.length === 0) {
                    vscode.window.showInformationMessage('No issues found in the document');
                    return;
                }

                // Store current issues

                // 写入临时文件
                try {
                    const docFileName = path.basename(editor.document.fileName);
                    const tmpPath = path.join(require('os').tmpdir(), `${docFileName}.review_issues.json`);
                    fs.writeFileSync(tmpPath, JSON.stringify(reviewResult.issues, null, 2), 'utf8');
                } catch (e) {
                    vscode.window.showWarningMessage('Failed to save last review issues.');
                }

                // Apply decorations
                applyDecorations(editor, reviewResult.issues);

                vscode.window.showInformationMessage(`Found ${reviewResult.issues.length} issues in the document`);
            } catch (error) {
                vscode.window.showErrorMessage(`Document review failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        });

    } catch (error) {
        vscode.window.showErrorMessage(`Document review error: ${error instanceof Error ? error.message : String(error)}`);
    }
}

function buildDocumentReviewPrompt(content: string, rules: string): string {
    return `你是一个专业且严谨的测试人员,负责评估用户手册的质量,请根据以下标准进行检查并且返回json格式的结果.

所依赖的规则:
${rules}

文档内容:
${content}

请返回一个如下结构的JSON格式应答, "startLine"是需要修正内容的起始行号，"endLine"是结束行号, 如果要修改的内容为多行（包含换行符)，则起始行号和结束行号应该不一样, "original"是需要修正的内容,要包含一整行或多个整行, "fixed"是修正后的建议内容，也要包含一整行或多个整行, "description"是修正的原因，多个问题用逗号分隔， 行号从1开始, 空行也要计算行号，如果没有需要修正的内容，返回空数组:
{
  "issues": [
    {
      "startLine": <line_number>,
      "endLine": <line_number>,
      "description": "<description_of_issue>",
      "original": "<original_content>",
      "fixed": "<suggested_fixed_content>"
    }
  ]
}`
}

function parseAIResponse(response: string): DocumentReviewResponse | null {
    try {
        // Try to extract JSON from the response
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            console.error('No JSON found in AI response');
            return null;
        }

        const parsed = JSON.parse(jsonMatch[0]) as DocumentReviewResponse;

        // Validate the structure
        if (!parsed.issues || !Array.isArray(parsed.issues)) {
            console.error('Invalid response structure');
            return null;
        }

        return parsed;
    } catch (error) {
        console.error('Failed to parse AI response:', error);
        return null;
    }
}

function applyDecorations(editor: vscode.TextEditor, issues: DocumentIssue[]): void {
    const issueRanges: vscode.Range[] = [];
    let suggestionDecorations: any[] = [];
    let lineOffset = 0;
    const doc = editor.document;
    // 复制一份 issues，避免直接修改原始 currentIssues
    let adjustedIssues = issues.map(issue => ({ ...issue }));
    currentIssues = adjustedIssues;

    for (let idx = 0; idx < adjustedIssues.length; idx++) {
        let issue = adjustedIssues[idx];
        // 动态查找原文
        const originalLines = issue.original.split(/\r?\n/);
        let foundStart = -1;
        let foundEnd = -1;
        for (let i = Math.max(0, issue.startLine - 1 + lineOffset); i <= doc.lineCount - originalLines.length + lineOffset; i++) {
            let match = true;
            for (let j = 0; j < originalLines.length; j++) {
                if ((doc.lineAt(i + j).text.trimEnd() !== originalLines[j].trimEnd())) {
                    match = false;
                    break;
                }
            }
            if (match) {
                foundStart = i;
                foundEnd = i + originalLines.length - 1;
                break;
            }
        }
        // 没找到就用AI行号
        const startLine = foundStart !== -1 ? foundStart : Math.max(0, issue.startLine - 1 + lineOffset);
        const endLine = foundEnd !== -1 ? foundEnd : Math.min(doc.lineCount - 1, issue.endLine - 1 + lineOffset);
        issue.startLine = startLine;
        issue.endLine = endLine;

        // 高亮原文
        const issueRange = new vscode.Range(startLine, 0, endLine, doc.lineAt(endLine).text.length);
        issueRanges.push(issueRange);

        // 插入 suggestion，每一行 fixed 都显示
        const fixedLines = issue.fixed.split(/\r?\n/);
        for (let k = 0; k < fixedLines.length; k++) {
            const suggestionLine = endLine + 1 + k;
            suggestionDecorations.push({
                range: new vscode.Range(suggestionLine, 0, suggestionLine, 0),
                renderOptions: {
                    after: {
                        contentText: fixedLines[k],
                        backgroundColor: 'rgba(0, 100, 0, 0.84)',
                        color: 'white',
                        fontStyle: 'italic'
                    }
                }
            });
        }
        // 后续 issue 的行号要加上本次插入的 fixed 行数
        // lineOffset += fixedLines.length;
        // // 下一个 issue 的 startLine/endLine 需要加上 lineOffset
        // for (let j = idx + 1; j < adjustedIssues.length; j++) {
        //     adjustedIssues[j].startLine += fixedLines.length;
        //     adjustedIssues[j].endLine += fixedLines.length;
        // }
    }

    // 应用高亮
    editor.setDecorations(issueDecorationType, issueRanges);
    editor.setDecorations(suggestionDecorationType, suggestionDecorations);

    // 刷新 CodeLens
    if (codeLensProvider) {
        codeLensProvider.refresh();
    }
}

function clearDecorations(): void {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
        editor.setDecorations(issueDecorationType, []);
        editor.setDecorations(suggestionDecorationType, []);
    }
    currentIssues = [];

    // 刷新 CodeLens
    if (codeLensProvider) {
        codeLensProvider.refresh();
    }
}

// CodeLens provider for showing action buttons
class DocumentReviewCodeLensProvider implements vscode.CodeLensProvider {
    private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;

    public refresh(): void {
        this._onDidChangeCodeLenses.fire();
    }

    provideCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] | Thenable<vscode.CodeLens[]> {
        const codeLenses: vscode.CodeLens[] = [];

        if (currentIssues.length === 0) {
            return codeLenses;
        }

        for (const issue of currentIssues) {
            const range = new vscode.Range(issue.startLine, 0, issue.startLine, 0);

            // Apply button
            const applyCommand: vscode.Command = {
                title: "✓ Apply",
                command: "sourceseek.applyDocumentSuggestion",
                arguments: [{
                    startLine: issue.startLine,
                    endLine: issue.endLine,
                    suggestion: issue.fixed
                }]
            };
            codeLenses.push(new vscode.CodeLens(range, applyCommand));

            // Dismiss button
            const dismissCommand: vscode.Command = {
                title: "✕ Dismiss",
                command: "sourceseek.dismissDocumentIssue",
                arguments: [{
                    startLine: issue.startLine,
                    endLine: issue.endLine
                }]
            };
            codeLenses.push(new vscode.CodeLens(range, dismissCommand));

            // Show description
            const descCommand: vscode.Command = {
                title: `💡 ${issue.description}`,
                command: "sourceseek.showIssueDescription",
                arguments: [{
                    description: issue.description,
                    original: issue.original,
                    fixed: issue.fixed
                }]
            };
            codeLenses.push(new vscode.CodeLens(range, descCommand));
        }

        return codeLenses;
    }
}

// Hover provider for showing issue details and action buttons
class DocumentReviewHoverProvider implements vscode.HoverProvider {
    provideHover(document: vscode.TextDocument, position: vscode.Position): vscode.ProviderResult<vscode.Hover> {
        if (currentIssues.length === 0) {
            return null;
        }

        // Find if the position is within any issue range
        const lineNumber = position.line; // Convert to 1-based
        const issue = currentIssues.find(issue =>
            lineNumber >= issue.startLine && lineNumber <= issue.endLine
        );

        if (!issue) {
            return null;
        }

        // Create hover content with action buttons
        const markdown = new vscode.MarkdownString();
        markdown.isTrusted = true;
        markdown.supportHtml = true;

        markdown.appendMarkdown(`**Issue:** ${issue.description}\n\n`);
        markdown.appendMarkdown(`**Suggestion:** ${issue.fixed}\n\n`);

        // Add action buttons
        const applyCommand = `command:sourceseek.applyDocumentSuggestion?${encodeURIComponent(JSON.stringify({
            startLine: issue.startLine,
            endLine: issue.endLine,
            suggestion: issue.fixed
        }))}`;

        const dismissCommand = `command:sourceseek.dismissDocumentIssue?${encodeURIComponent(JSON.stringify({
            startLine: issue.startLine,
            endLine: issue.endLine
        }))}`;

        markdown.appendMarkdown(`[Apply](${applyCommand} "Apply this suggestion") `);
        //markdown.appendMarkdown(`[$(x) Dismiss](${dismissCommand} "Dismiss this issue")`);

        return new vscode.Hover(markdown);
    }
}

// Command to apply a suggestion
async function applyDocumentSuggestion(args: any): Promise<void> {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }

        const { startLine, endLine, suggestion } = args;

        // startLine and endLine are already 0-based from applyDecorations
        const range = new vscode.Range(startLine, 0, endLine, editor.document.lineAt(endLine).text.length);

        await editor.edit(editBuilder => {
            editBuilder.replace(range, suggestion);
        });

        // Remove this issue from current issues
        currentIssues = currentIssues.filter(issue =>
            !(issue.startLine === startLine && issue.endLine === endLine)
        );

        // Refresh decorations and CodeLens
        if (currentIssues.length > 0) {
            applyDecorations(editor, currentIssues);
        } else {
            clearDecorations();
        }

        if (codeLensProvider) {
            codeLensProvider.refresh();
        }

        vscode.window.showInformationMessage('Suggestion applied successfully');
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to apply suggestion: ${error instanceof Error ? error.message : String(error)}`);
    }
}

// Command to dismiss an issue
async function dismissDocumentIssue(args: any): Promise<void> {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }

        const { startLine, endLine } = args;

        // Remove this issue from current issues
        currentIssues = currentIssues.filter(issue =>
            !(issue.startLine === startLine && issue.endLine === endLine)
        );

        // Refresh decorations and CodeLens
        if (currentIssues.length > 0) {
            applyDecorations(editor, currentIssues);
        } else {
            clearDecorations();
        }

        if (codeLensProvider) {
            codeLensProvider.refresh();
        }

        vscode.window.showInformationMessage('Issue dismissed');
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to dismiss issue: ${error instanceof Error ? error.message : String(error)}`);
    }
}

// Command to show issue description
async function showIssueDescription(args: any): Promise<void> {
    const { description, original, fixed } = args;
    const message = `**Issue:** ${description}\n\n**Original:**\n${original}\n\n**Suggested Fix:**\n${fixed}`;
    vscode.window.showInformationMessage(message, { modal: false });
}

// Show Last Review 命令实现
async function showLastReviewIssues() {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const docFileName = path.basename(editor.document.fileName);
        const tmpPath = path.join(require('os').tmpdir(), `${docFileName}.review_issues.json`);
        if (!fs.existsSync(tmpPath)) {
            vscode.window.showWarningMessage('No last review issues found.');
            return;
        }
        const issuesRaw = fs.readFileSync(tmpPath, 'utf8');
        const issues = JSON.parse(issuesRaw);
        if (!Array.isArray(issues)) {
            vscode.window.showWarningMessage('Invalid last review issues file.');
            return;
        }
        currentIssues = issues;
        applyDecorations(editor, currentIssues);
        if (codeLensProvider) {
            codeLensProvider.refresh();
        }
        vscode.window.showInformationMessage('Last review issues loaded and highlighted.');
    } catch (e) {
        vscode.window.showErrorMessage('Failed to load last review issues.');
    }
}

// Register all document review commands and providers
export function registerDocumentReviewCommand(context: vscode.ExtensionContext): void {
    // Create and register CodeLens provider
    codeLensProvider = new DocumentReviewCodeLensProvider();
    const codeLensDisposable = vscode.languages.registerCodeLensProvider(
        { scheme: 'file', language: 'restructuredtext' },
        codeLensProvider
    );

    // Register main review command
    const reviewCommand = vscode.commands.registerCommand('sourceseek.reviewDocument', reviewDocument);

    // Register action commands
    const applyCommand = vscode.commands.registerCommand('sourceseek.applyDocumentSuggestion', applyDocumentSuggestion);
    const dismissCommand = vscode.commands.registerCommand('sourceseek.dismissDocumentIssue', dismissDocumentIssue);
    const showDescCommand = vscode.commands.registerCommand('sourceseek.showIssueDescription', showIssueDescription);

    // Register hover provider
    const hoverProvider = vscode.languages.registerHoverProvider(
        { scheme: 'file', language: 'restructuredtext' },
        new DocumentReviewHoverProvider()
    );

    // Register Show Last Review command
    const showLastReviewCmd = vscode.commands.registerCommand('sourceseek.showLastReview', showLastReviewIssues);

    // Clean up decorations when editor changes
    const onDidChangeActiveEditor = vscode.window.onDidChangeActiveTextEditor(() => {
        //clearDecorations();
    });

    context.subscriptions.push(
        codeLensDisposable,
        reviewCommand,
        applyCommand,
        dismissCommand,
        showDescCommand,
        hoverProvider,
        onDidChangeActiveEditor,
        showLastReviewCmd
    );
}
