import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { requestAI } from './ReviewCode';
import { g_context } from './globalSettings';
import { getConfiguration} from './UtilFuns';
import { reviewOutputChannel } from './ReviewCode';


// Define the QuickAI history entry structure
export interface QuickAIHistoryEntry {
    timestamp: Date;
    timecost: number;
    context: 'terminal' | 'editor';
    engine: string;
    prompt: string;
    response: string;
    languageId?: string;
}

// Global QuickAI history storage
let quickAIHistory: QuickAIHistoryEntry[] = [];
const QUICK_AI_HISTORY_LIMIT = 50;
const QUICK_AI_HISTORY_KEY = 'quickAIHistory';
let historyFilePath: string | undefined = undefined;
/**
 * Add an entry to QuickAI history
 */
function addToQuickAIHistory(entry: QuickAIHistoryEntry): void {
    quickAIHistory.unshift(entry); // Add to beginning (newest first)
    
    // Limit history size
    if (quickAIHistory.length > QUICK_AI_HISTORY_LIMIT) {
        quickAIHistory = quickAIHistory.slice(0, QUICK_AI_HISTORY_LIMIT);
    }
    
    // Save to persistent storage
    if (g_context) {
        saveQuickAIHistory(g_context);
    }
}

/**
 * Save QuickAI history to persistent storage
 */
async function saveQuickAIHistory(context: vscode.ExtensionContext): Promise<void> {
    if (!historyFilePath) {
        return;
    }
    
    try {
        // Convert Date objects to ISO strings for serialization
        const serializableHistory = quickAIHistory.map(entry => ({
            ...entry,
            timestamp: entry.timestamp.toISOString()
        }));
        
        // Ensure the directory exists
        const dir = path.dirname(historyFilePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // Write history to file
        fs.writeFileSync(historyFilePath, JSON.stringify(serializableHistory, null, 2));
    } catch (error) {
        console.error('Failed to save QuickAI history:', error);
        // Don't show error message to user to avoid disruption
    }
}

/**
 * Load QuickAI history from file
 */
async function loadQuickAIHistory(): Promise<void> {
    if (!historyFilePath || !fs.existsSync(historyFilePath)) {
        return;
    }
    
    try {
        const data = fs.readFileSync(historyFilePath, 'utf8');
        const savedHistory = JSON.parse(data) as Array<{
            timestamp: string;
            timecost: number;
            context: 'terminal' | 'editor';
            prompt: string;
            engine: string;
            response: string;
            languageId?: string;
        }>;
        
        // Convert ISO strings back to Date objects
        quickAIHistory = savedHistory.map(entry => ({
            ...entry,
            timestamp: new Date(entry.timestamp)
        }));
    } catch (error) {
        console.error('Failed to load QuickAI history:', error);
        // Start with empty history if loading fails
        quickAIHistory = [];
    }
}

/**
 * Show QuickAI history dialog with copy functionality
 */
async function showQuickAIHistory(): Promise<void> {
    if (quickAIHistory.length === 0) {
        vscode.window.showInformationMessage('No QuickAI history available.');
        return;
    }

    // Create quick pick items from history
    const items: vscode.QuickPickItem[] = quickAIHistory.map((entry, index) => ({
        label: `$(history) ${entry.prompt.substring(0, 50)}${entry.prompt.length > 50 ? '...' : ''}`,
        description: `${entry.context} • ${entry.timestamp.toLocaleString()}`,
        detail: entry.response.substring(0, 100) + (entry.response.length > 100 ? '...' : ''),
        alwaysShow: true
    }));

    // Add a clear history option
    items.unshift({
        label: '$(clear-all) Clear History',
        description: 'Remove all QuickAI history entries',
        alwaysShow: true
    });

    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: 'Select a history entry to copy or clear history',
        matchOnDescription: true,
        matchOnDetail: true
    });

    if (!selected) {
        return;
    }

    if (selected.label === '$(clear-all) Clear History') {
        // Clear history
        const confirm = await vscode.window.showQuickPick(['Yes', 'No'], {
            placeHolder: 'Are you sure you want to clear all QuickAI history?'
        });
        if (confirm === 'Yes') {
            quickAIHistory = [];
            vscode.window.showInformationMessage('QuickAI history cleared.');
            // Save the cleared state
            if (g_context) {
                saveQuickAIHistory(g_context);
            }
        }
        return;
    }

    // Find the selected history entry
    const selectedIndex = items.indexOf(selected) - 1; // Subtract 1 because we added clear option at index 0
    if (selectedIndex >= 0 && selectedIndex < quickAIHistory.length) {
        const selectedEntry = quickAIHistory[selectedIndex];
        
        // Show options for the selected entry
        const action = await vscode.window.showQuickPick([
            'Copy Prompt',
            'Copy Response',
            'Copy Both',
            'View Details'
        ], {
            placeHolder: 'Choose an action for this history entry'
        });

        if (!action) {
            return;
        }

        switch (action) {
            case 'Copy Prompt':
                await vscode.env.clipboard.writeText(selectedEntry.prompt);
                vscode.window.showInformationMessage('Prompt copied to clipboard.');
                break;
            case 'Copy Response':
                await vscode.env.clipboard.writeText(selectedEntry.response);
                vscode.window.showInformationMessage('Response copied to clipboard.');
                break;
            case 'Copy Both':
                const combined = `Prompt: ${selectedEntry.prompt}\n\nResponse: ${selectedEntry.response}`;
                await vscode.env.clipboard.writeText(combined);
                vscode.window.showInformationMessage('Prompt and response copied to clipboard.');
                break;
            case 'View Details':
                // Show detailed view in a new document
                const doc = await vscode.workspace.openTextDocument({
                    content: `QuickAI History Entry\n\nTimestamp: ${selectedEntry.timestamp.toLocaleString()}\nContext: ${selectedEntry.context}\nLanguage: ${selectedEntry.languageId || 'N/A'}\n\nPROMPT:\n${selectedEntry.prompt}\n\nRESPONSE:\n${selectedEntry.response}`,
                    language: 'markdown'
                });
                await vscode.window.showTextDocument(doc);
                break;
        }
    }
}

/**
 * Show combined QuickAI dialog with last 5 history entries
 * Each history entry shows request and response on separate lines
 * Clicking a history entry populates the input box
 */
async function showQuickAIWithHistory(placeHolder: string): Promise<string | undefined> {
  return new Promise<string | undefined>((resolve) => {
        // 创建quickPick实例
        const quickPick = vscode.window.createQuickPick<vscode.QuickPickItem & { prompt?: string; response?: string }>();
        quickPick.placeholder = placeHolder;
        quickPick.canSelectMany = false;
        quickPick.matchOnDescription = true;
        quickPick.matchOnDetail = true;
        
        // 生成历史记录项
        const historyItems: (vscode.QuickPickItem & { prompt?: string; response?: string })[] = [];
        for (let index = 0; index < quickAIHistory.length; index++) {
            if (index > 10) {
                break;
            }
            const item = quickAIHistory[index];
            const preview = item.prompt.length > 50
                ? item.prompt.substring(0, 50) + '...'
                : item.prompt;
            
            historyItems.push({
                label: `$(history) ${preview}`,
                description: `Request #${quickAIHistory.length - index}`,
                prompt: item.prompt,
                response: item.response // 添加response属性
            });
        }
        
        // 添加清除历史记录选项
        if (quickAIHistory.length > 0) {
            historyItems.push({
                label: `$(trash) clear-all `,
                description: `clear all QuickAI history`,
                prompt: undefined,
                response: undefined
            });
        }
        historyItems.push({
            label: `$(detail) show-full`,
            description: `show full QuickAI history`,
            prompt: undefined,
            response: undefined
        });
        
        quickPick.items = historyItems;
        let selectionHandled = false;
        let input = '';
        
        // 处理历史记录项选择变化事件 - 只填充输入框，不返回，同时复制response到剪贴板
        quickPick.onDidChangeSelection((selection) => {
            if (selection.length === 1) {
                const selectedItem = selection[0];
                if (selectedItem.label.includes('clear-all')) {
                    quickAIHistory = [];
                    quickPick.items = [];
                    quickPick.value = '';
                    // Save the cleared state
                    if (g_context) {
                        saveQuickAIHistory(g_context);
                    }
                } else if (selectedItem.label.includes('show-full')) {
                    showQuickAIHistory(); 
                } else if (selectedItem.prompt) {
                    quickPick.value = selectedItem.prompt;
                    // 当选择历史记录时，将对应的response复制到剪贴板
                    if (selectedItem.response) {
                        vscode.env.clipboard.writeText(selectedItem.response);
                        vscode.window.showInformationMessage('Response copied to clipboard');
                    }
                }
                selectionHandled = true;
                setTimeout(() => {
                    selectionHandled = false;
                }, 10);
            }
        });
        
        // 处理确认事件 - 只在输入内容或选择有效项时返回
        quickPick.onDidAccept(() => {
            if (selectionHandled && input != quickPick.value.trim()) {
                input = quickPick.value.trim();
                return;
            }
            // 优先返回用户输入的内容
            if (quickPick.value.trim()) {
                const value = quickPick.value.trim();
                setTimeout(() => {
                    quickPick.dispose();
                    resolve(value);
                }, 10);
            } else {
                const selectedItem = quickPick.selectedItems[0];
                if (selectedItem && selectedItem.prompt) {
                    setTimeout(() => {
                        quickPick.dispose();
                        resolve(selectedItem.prompt);
                    }, 10);
                } else {
                    setTimeout(() => {
                        quickPick.dispose();
                        resolve(undefined);
                    }, 10);
                }
            }
        });
        
        // 处理取消事件
        quickPick.onDidHide(() => {
            setTimeout(() => {
                quickPick.dispose();
                resolve(undefined);
            }, 10);
        });
        
        // 显示quickPick
        quickPick.show();
    });
}

/**
 * Register a quick AI prompt command that:
 * - Shows an input box on Ctrl+K
 * - If focus is in the integrated terminal, asks AI to generate a single command and inserts it into the terminal (without executing)
 * - If focus is in the editor, asks AI to generate code and inserts it at the current cursor/selection
 */

export function registerQuickAICommand(context: vscode.ExtensionContext): void {
  // Set up history file path
  historyFilePath = path.join(context.globalStorageUri.fsPath, 'quickAIHistory.json');

  // Load saved history on activation
  loadQuickAIHistory();
  
  // Register the main QuickAI command
  const quickAIDisposable = vscode.commands.registerCommand(
    'sourceseek.quickAI',
    async (args?: { context?: 'terminal' | 'editor' }) => {
      try {
        const inferredContext: 'terminal' | 'editor' = inferContext(args);

        // Show combined dialog with history
        let placeholder = '';
        if (inferredContext === 'terminal') {
          placeholder = 'Enter a prompt to generate command line or choose from history';
        } else {
          placeholder = 'Enter a prompt to generate code or choose from history';
        }
        const userInput = await showQuickAIWithHistory(placeholder);
        
        // If user selected a history action or cancelled, return
        if (userInput === undefined) {
          return;
        }

        let finalOutput = '';
        let timeCost = '0';

        // Show progress while waiting for AI response
        await vscode.window.withProgress({
          location: vscode.ProgressLocation.Notification,
          title: 'QuickAI is thinking...',
          cancellable: false
        }, async (progress) => {
          // Set initial progress
          progress.report({ message: 'Starting...', increment: 0 });
          
          // Create a timer that updates progress every 500ms by 10% up to 90%
          let currentProgress = 0;
          const interval = setInterval(() => {
            if (currentProgress < 90) {
              currentProgress += 10;
              progress.report({
                message: `Processing... ${currentProgress}%`,
                increment: 10
              });
            }
          }, 500);
          
          try {
            if (inferredContext === 'terminal') {
              const prompt = buildTerminalPrompt(userInput);
              const startTime = Date.now();
              const ai = (await requestAI(prompt))?.trim();
              const endTime = Date.now();
              timeCost = ((endTime - startTime) / 1000).toFixed(2);
              reviewOutputChannel.appendLine(`[${new Date().toLocaleString()}] ${prompt}, cost ${timeCost} seconds`);
              let aibr = '';
              if (ai) {
                aibr = ai.replace(/^<think>[\s\S]*?<\/think>\s*/i, '');
              }
            
              const commandText = aibr ? stripFences(aibr) : '';
              if (!commandText) {
                vscode.window.showInformationMessage('No command generated by AI.');
                return;
              }
              finalOutput = commandText;
              
              const terminal = vscode.window.activeTerminal || vscode.window.terminals[0];
              if (!terminal) {
                vscode.window.showErrorMessage('No active terminal found. Open a terminal and try again.');
                return;
              }
              terminal.sendText(commandText, false); // paste without executing
            } else {
              const editor = vscode.window.activeTextEditor;
              if (!editor) {
                vscode.window.showErrorMessage('No active editor found.');
                return;
              }
              const languageId = editor.document.languageId;
              const selection = editor.selection;
              const selectedText = editor.document.getText(selection);

              const prompt = buildEditorPrompt(userInput, languageId, selectedText);
              const startTime = Date.now();
              const ai = (await requestAI(prompt))?.trim();
              const endTime = Date.now();
              timeCost = ((endTime - startTime) / 1000).toFixed(2);
              reviewOutputChannel.appendLine(`[${new Date().toLocaleString()}] ${prompt}, cost ${timeCost} seconds`);

              const code = ai ? stripFences(ai) : '';
              if (!code) {
                vscode.window.showInformationMessage('No code generated by AI.');
                return;
              }
              // 过滤掉开头用<think></think>包起来的内容
              let filteredCode = code.replace(/^<think>[\s\S]*?<\/think>\s*/i, '');
              finalOutput = filteredCode;
              
              await editor.edit((eb) => {
                if (selection && !selection.isEmpty) {
                  eb.replace(selection, filteredCode);
                } else {
                  eb.insert(editor.selection.active, filteredCode);
                }
              });
            }
          } finally {
            // Clear the interval and set progress to 100%
            clearInterval(interval);
            progress.report({
              message: 'Completed!',
              increment: 10
            });
          }
        });

        // Add to history
        const aiModel = getConfiguration().get("aiModel") as string || "DeepSeek-V3";
        const historyEntry: QuickAIHistoryEntry = {
          timestamp: new Date(),
          timecost: Number(timeCost),
          context: inferredContext,
          prompt: userInput,
          engine: aiModel,
          response: finalOutput,
          languageId: inferredContext === 'editor' ? vscode.window.activeTextEditor?.document.languageId : undefined
        };
        addToQuickAIHistory(historyEntry);

      } catch (err) {
        vscode.window.showErrorMessage(`Quick AI error: ${err instanceof Error ? err.message : String(err)}`);
      }
    }
  );

  // Remove the separate history command since we have a unified dialog
  context.subscriptions.push(quickAIDisposable);
}

function inferContext(args?: { context?: 'terminal' | 'editor' }): 'terminal' | 'editor' {
  if (args?.context === 'terminal' || args?.context === 'editor') return args.context;
  // Fallback heuristic if args are not provided: prefer editor when a text editor is active
  return vscode.window.activeTextEditor ? 'editor' : 'terminal';
}

function buildTerminalPrompt(task: string): string {
  return [
    'You are a command-line assistant on Linux. Output ONLY a single shell command that fulfills the task below.',
    'Do not include explanations, comments, or code fences. No backticks.',
    'Prefer safe, idempotent commands when possible.',
    'Shell: bash.',
    `Task: ${task}.`,
    'Command:'
  ].join('\n');
}

function buildEditorPrompt(task: string, languageId: string, selected?: string): string {
  const lines: string[] = [];
  lines.push('You are a coding assistant. Output ONLY the code snippet, no explanations, no code fences.');
  lines.push(`Language: ${languageId}`);
  lines.push(`Task: ${task}`);
  if (selected && selected.trim().length > 0) {
    lines.push('If relevant, complete or adapt the following selected code context:');
    lines.push(selected);
  }
  return lines.join('\n');
}

function stripFences(text: string): string {
  // Remove Markdown code fences if present

  const fenceRegex = /^```[\s\S]*?\n([\s\S]*?)\n```\s*$/;
  const m = text.match(fenceRegex);
  if (m && m[1]) return m[1];
  // Also strip inline triple backticks if used without language
  const fenceRegex2 = /^```\n([\s\S]*?)\n```\s*$/;
  const m2 = text.match(fenceRegex2);
  if (m2 && m2[1]) return m2[1];
  // Strip leading/trailing backticks or quotes
  return text.replace(/^```+|```+$/g, '').trim();
}