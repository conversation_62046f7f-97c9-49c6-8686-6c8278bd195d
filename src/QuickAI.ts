import * as vscode from 'vscode';
import { requestAI } from './ReviewCode';

/**
 * Register a quick AI prompt command that:
 * - Shows an input box on Ctrl+K
 * - If focus is in the integrated terminal, asks AI to generate a single command and inserts it into the terminal (without executing)
 * - If focus is in the editor, asks AI to generate code and inserts it at the current cursor/selection
 */
export function registerQuickAICommand(context: vscode.ExtensionContext): void {
  const disposable = vscode.commands.registerCommand(
    'sourceseek.quickAI',
    async (args?: { context?: 'terminal' | 'editor' }) => {
      try {
        const inferredContext: 'terminal' | 'editor' = inferContext(args);

        const userInput = await vscode.window.showInputBox({
          prompt: inferredContext === 'terminal' ? 'Describe the command you want (AI will output a single shell command)' : 'Describe the code you want to insert (AI will output only code snippet)',
          placeHolder: inferredContext === 'terminal' ? 'e.g., find large files modified in last 24h' : 'e.g., write a C++ function to parse a CSV line',
          ignoreFocusOut: true,
        });
        if (!userInput) {
          return;
        }

        if (inferredContext === 'terminal') {
          const prompt = buildTerminalPrompt(userInput);
          const ai = (await requestAI(prompt))?.trim();
          const commandText = ai ? stripFences(ai) : '';
          if (!commandText) {
            vscode.window.showInformationMessage('No command generated by AI.');
            return;
          }
          const terminal = vscode.window.activeTerminal || vscode.window.terminals[0];
          if (!terminal) {
            vscode.window.showErrorMessage('No active terminal found. Open a terminal and try again.');
            return;
          }
          terminal.sendText(commandText, false); // paste without executing
        } else {
          const editor = vscode.window.activeTextEditor;
          if (!editor) {
            vscode.window.showErrorMessage('No active editor found.');
            return;
          }
          const languageId = editor.document.languageId;
          const selection = editor.selection;
          const selectedText = editor.document.getText(selection);

          const prompt = buildEditorPrompt(userInput, languageId, selectedText);
          const ai = (await requestAI(prompt))?.trim();
          const code = ai ? stripFences(ai) : '';
          if (!code) {
            vscode.window.showInformationMessage('No code generated by AI.');
            return;
          }
          await editor.edit((eb) => {
            if (selection && !selection.isEmpty) {
              eb.replace(selection, code);
            } else {
              eb.insert(editor.selection.active, code);
            }
          });
        }
      } catch (err) {
        vscode.window.showErrorMessage(`Quick AI error: ${err instanceof Error ? err.message : String(err)}`);
      }
    }
  );

  context.subscriptions.push(disposable);
}

function inferContext(args?: { context?: 'terminal' | 'editor' }): 'terminal' | 'editor' {
  if (args?.context === 'terminal' || args?.context === 'editor') return args.context;
  // Fallback heuristic if args are not provided: prefer editor when a text editor is active
  return vscode.window.activeTextEditor ? 'editor' : 'terminal';
}

function buildTerminalPrompt(task: string): string {
  return [
    'You are a command-line assistant on Linux. Output ONLY a single shell command that fulfills the task below.',
    'Do not include explanations, comments, or code fences. No backticks.',
    'Prefer safe, idempotent commands when possible.',
    'Shell: bash.',
    `Task: ${task}`,
  ].join('\n');
}

function buildEditorPrompt(task: string, languageId: string, selected?: string): string {
  const lines: string[] = [];
  lines.push('You are a coding assistant. Output ONLY the code snippet, no explanations, no code fences.');
  lines.push(`Language: ${languageId}`);
  lines.push(`Task: ${task}`);
  if (selected && selected.trim().length > 0) {
    lines.push('If relevant, complete or adapt the following selected code context:');
    lines.push(selected);
  }
  return lines.join('\n');
}

function stripFences(text: string): string {
  // Remove Markdown code fences if present
  const fenceRegex = /^```[\s\S]*?\n([\s\S]*?)\n```\s*$/;
  const m = text.match(fenceRegex);
  if (m && m[1]) return m[1];
  // Also strip inline triple backticks if used without language
  const fenceRegex2 = /^```\n([\s\S]*?)\n```\s*$/;
  const m2 = text.match(fenceRegex2);
  if (m2 && m2[1]) return m2[1];
  // Strip leading/trailing backticks or quotes
  return text.replace(/^```+|```+$/g, '').trim();
}

