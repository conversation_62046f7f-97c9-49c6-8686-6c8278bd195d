import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Show About dialog and open README.md
 */
export async function showAboutDialog(context: vscode.ExtensionContext): Promise<void> {
    try {
        // Get extension path and package.json
        const extensionPath = context.extensionPath;
        const packageJsonPath = path.join(extensionPath, 'package.json');
        const readmePath = path.join(extensionPath, 'readme.md');

        // Read version from package.json
        let version = 'Unknown';
        try {
            const packageContent = fs.readFileSync(packageJsonPath, 'utf8');
            const packageJson = JSON.parse(packageContent);
            version = packageJson.version || 'Unknown';
        } catch (error) {
            console.error('Failed to read package.json:', error);
        }

        // Open README.md file
        try {
            if (fs.existsSync(readmePath)) {
                await vscode.commands.executeCommand('markdown.showPreview', vscode.Uri.file(readmePath));
            } else {
                vscode.window.showWarningMessage('README.md file not found in extension folder');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open README.md: ${error instanceof Error ? error.message : String(error)}`);
        }

        // Show version dialog
        const message = `SourceSeek Extension\n\nVersion: ${version}\nFeedback: <EMAIL>\n\nA powerful C/C++ code navigation tool for VS Code`;
        const action = await vscode.window.showInformationMessage(
            message,
            { modal: true },
            'OK',
            'Copy Email',
            'View Documentation'
        );

        // Handle user action
        if (action === 'Copy Email') {
            await vscode.env.clipboard.writeText('<EMAIL>');
            vscode.window.showInformationMessage('Email address copied to clipboard');
        } else if (action === 'View Documentation') {
            // Preview README.md as markdown if user wants to view documentation
            try {
                if (fs.existsSync(readmePath)) {
                    await vscode.commands.executeCommand('markdown.showPreview', vscode.Uri.file(readmePath));
                }
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to preview documentation: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

    } catch (error) {
        vscode.window.showErrorMessage(`Failed to show About dialog: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Register About command
 */
export function registerAboutCommand(context: vscode.ExtensionContext): void {
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.about', async () => {
            await showAboutDialog(context);
        })
    );
}
