import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { requestAI, reviewOutputChannel } from './ReviewCode';

// Decorations for translation review highlights
const translationDecorationLeft = vscode.window.createTextEditorDecorationType({ backgroundColor: 'rgba(255, 166, 0, 0.2)' });
const translationDecorationRight = vscode.window.createTextEditorDecorationType({ backgroundColor: 'rgba(185, 209, 51, 0.2)' });

// Global flag to control hover highlight feature
let isHoverHighlightEnabled = false;

// Global dictionary to store ranges by file name
const fileRangesMap = new Map<string, { curRanges: Array<{ start: number; end: number }>; otherRanges: Array<{ start: number; end: number }> }>();

// Decorations for hover sentence alignment
const hoverDecorationCurrent = vscode.window.createTextEditorDecorationType({
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    border: '1px solid rgba(255, 215, 0, 0.6)'
});
const hoverDecorationOther = vscode.window.createTextEditorDecorationType({
    backgroundColor: 'rgba(0, 200, 255, 0.2)',
    border: '1px solid rgba(0, 200, 255, 0.6)'
});

// Alignment mapping types
interface AlignmentSegment {
    src_start_line: number; // 1-based inclusive
    src_end_line: number;   // 1-based inclusive
    dest_start_line: number; // 1-based inclusive
    dest_end_line: number;   // 1-based inclusive
}
interface AlignmentFile {
    sourcePath: string;
    destPath: string;
    segments: AlignmentSegment[];
}

async function loadAlignmentForOriginal(originalPath: string): Promise<AlignmentFile | null> {
    try {
        const uri = vscode.Uri.file(originalPath + '.trans');
        const data = await vscode.workspace.fs.readFile(uri);
        const text = Buffer.from(data).toString('utf8');
        const obj = JSON.parse(text);
        if (obj && Array.isArray(obj.segments)) {
            return obj as AlignmentFile;
        }
    } catch { /* ignore */ }
    return null;
}

function get_en_file(originalPath:string) {
    const dir = path.dirname(originalPath);
    const ext = path.extname(originalPath);
    const base = path.basename(originalPath, ext);
    let candidatePath = ''
    if (base.endsWith('_zh_CN')) 
        candidatePath = path.join(dir, base.replace('_zh_CN', '_en') + ext);
    else
        candidatePath = path.join(dir, base + '_en'+ ext);
    return candidatePath;
}

function finding_cores_file(originalPath: string): string {
    const dir = path.dirname(originalPath);
    const ext = path.extname(originalPath);
    const base = path.basename(originalPath, ext);
    const suffix = ['_en'];
    for( const [index, value] of suffix.entries()) { 
        const candidatePath = base.endsWith(value) ? path.join(dir, base.slice(0, -3) + ext) : path.join(dir, base + value + ext);
        if (fs.existsSync(candidatePath))  {
            return candidatePath;
        }
    }
    if (base.endsWith('_zh_CN')) {
        const candidatePath = path.join(dir, base.replace('_zh_CN', '_en') + ext);
        if (fs.existsSync(candidatePath))  {
            return candidatePath;
        }
    }

    if (base.endsWith('_en')) {
        const candidatePath = path.join(dir, base.replace('_en', '_zh_CN') + ext);
        if (fs.existsSync(candidatePath))  {
            return candidatePath;
        }
    }
    return '';
}

async function buildAndSaveAlignment(originalEditor: vscode.TextEditor, translationEditor: vscode.TextEditor): Promise<void> {
    const originalText = originalEditor.document.getText();
    const translationText = translationEditor.document.getText();
    const originalPath = originalEditor.document.uri.fsPath;
    const destPath = translationEditor.document.uri.fsPath;

    const prompt = [
        'You are an expert bilingual aligner. Given ORIGINAL (Chinese) and TRANSLATION (English), segment both texts into paragraphs separated by blank lines (i.e., contiguous non-empty lines).',
        'Return a JSON with exact line ranges (1-based, inclusive) that map each original segment to its corresponding translated segment, only when the translated segment is the exact translation of that original segment.',
        'Output strictly this JSON format and nothing else:',
        '{"segments": [{"src_start_line": N, "src_end_line": M, "dest_start_line": A, "dest_end_line": B}, ...]}',
        'Rules:',
        '- Lines are counted in their respective files independently (ORIGINAL vs TRANSLATION).',
        '- Only include pairs where the dest segment is the translation of the src segment.',
        '- Do not include commentary or extra fields.',
        '',
        '<<<ORIGINAL\n', originalText, '\n>>>',
        '<<<TRANSLATION\n', translationText, '\n>>>'
    ].join('\n');

    const start = Date.now();
    const raw = await requestAI(prompt);
    const secs = ((Date.now() - start) / 1000).toFixed(2);
    if (!raw) {
        reviewOutputChannel.appendLine('Alignment: AI did not return a result.');
        return;
    }

    const match = raw.match(/\{[\s\S]*\}/);
    const jsonStr = match ? match[0] : raw;
    let parsed: any;
    try { parsed = JSON.parse(jsonStr); } catch {
        reviewOutputChannel.appendLine('Alignment: Failed to parse AI JSON.');
        return;
    }
    const segments: AlignmentSegment[] = Array.isArray(parsed?.segments) ? parsed.segments : [];
    const payload: AlignmentFile = { sourcePath: originalPath, destPath, segments };
    try {
        const uri = vscode.Uri.file(originalPath + '.trans');
        await vscode.workspace.fs.writeFile(uri, Buffer.from(JSON.stringify(payload, null, 2), 'utf8'));
        reviewOutputChannel.appendLine(`Alignment saved: ${uri.fsPath}`);
        reviewOutputChannel.appendLine(`[AI alignment time cost: ${secs} seconds]`);
    } catch (e) {
        reviewOutputChannel.appendLine(`Alignment: Failed to save .trans file: ${e instanceof Error ? e.message : String(e)}`);
    }
}


function hasChinese(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text);
}

function splitSegmentWithOffsets(text: string, isChinese: boolean): Array<{ start: number; end: number; }> {
    const ranges: Array<{ start: number; end: number; }> = [];
    const len = text.length;
    let start = 0;
    let lastNonEmpty = -1;

    for (let i = 0; i < len; i++) {
        const ch = text[i];
        if (ch === '\n') {
            // If we find two consecutive newlines (blank line), this marks a segment boundary
            if (i > 0 && i - lastNonEmpty > 1) {
                if (start < lastNonEmpty + 1) {
                    ranges.push({ start, end: lastNonEmpty + 1 });
                }
                start = i + 1;
            }
        } else if (ch !== ' ' && ch !== '\t') {
            lastNonEmpty = i;
        }
    }

    // Don't forget the last segment
    if (start < len) {
        ranges.push({ start, end: len });
    }
    return ranges;
}

function findCounterpartEditor(currentDoc: vscode.TextDocument): vscode.TextEditor | undefined {
    const curPath = currentDoc.uri.fsPath;
    const otherPath = finding_cores_file(curPath);

    return vscode.window.visibleTextEditors.find(e => e.document.uri.fsPath === otherPath);
}

export async function reviewTranslation(): Promise<void> {
    try {
        const leftEditor = vscode.window.activeTextEditor;
        let rightEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.Two);
        if (!leftEditor) {
            vscode.window.showErrorMessage('No active editor (left window)');
            return;
        }

        // Try to find/open counterpart if right editor is missing
        if (!rightEditor) {
            const leftPath = leftEditor.document.uri.fsPath;
            const leftTextSample = leftEditor.document.getText().slice(0, 2000);
            const looksChinese = /[\u4e00-\u9fff]/.test(leftTextSample);
            let candidate: string | null = null;
            candidate = finding_cores_file(leftPath);
            if (candidate && candidate !== leftPath) {
                try {
                    await vscode.workspace.fs.stat(vscode.Uri.file(candidate));
                    const doc = await vscode.workspace.openTextDocument(candidate);
                    await vscode.window.showTextDocument(doc, { viewColumn: vscode.ViewColumn.Two, preserveFocus: true, preview: true });
                    rightEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.Two && e.document.uri.fsPath === candidate)
                        || vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.Two);
                } catch {
                    // ignore if counterpart not found
                }
            }
        }

        if (!rightEditor) {
            vscode.window.showErrorMessage('Could not locate the counterpart file. Please open the original/translation file in the right editor and try again.');
            return;
        }

        // Decide which side is translation vs original (Chinese)
        const leftText = leftEditor.document.getText();
        const rightText = rightEditor.document.getText();
        const leftHasChinese = /[\u4e00-\u9fff]/.test(leftText);
        const rightHasChinese = /[\u4e00-\u9fff]/.test(rightText);

        const translationEditor = (!leftHasChinese && rightHasChinese) ? leftEditor : (!rightHasChinese && leftHasChinese) ? rightEditor : leftEditor;
        const originalEditor = (translationEditor === leftEditor) ? rightEditor : leftEditor;

        const translationText = translationEditor.document.getText();
        const originalText = originalEditor.document.getText();
        const translationFileName = path.basename(translationEditor.document.uri.fsPath);
        const originalFileName = path.basename(originalEditor.document.uri.fsPath);

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Reviewing translation…',
            cancellable: true
        }, async () => {
            // Clear output and previous highlights first
            reviewOutputChannel.clear();
            reviewOutputChannel.appendLine(`Reviewing translation: ${translationFileName} (translation) vs ${originalFileName} (original Chinese)`);
            reviewOutputChannel.show(true);
            translationEditor.setDecorations(translationDecorationLeft, []);
            originalEditor.setDecorations(translationDecorationRight, []);

            const prompt = [
                '你是双语翻译审校专家。给定：右侧窗口为中文原文(original)，左侧窗口为译文(translation)。',
                '请逐句对齐（以中文“。！？；”及换行、或英文句号/换行作为切分参考），检查：',
                '1) 译文是否误译/漏译/过度意译；2) 术语是否不一致；3) 语序与逻辑是否偏差；4) 是否可提供更优译法。',
                '输出严格JSON，且只输出JSON，不要附加解释：',
                '{"findings": [{"original": "中文原句", "translation": "对应译文", "issue": "问题归纳", "suggestion": "改进译法(若有)"}, ...]}',
                '最多返回50条有问题的句子，不要包含没有问题的句子。',
                '',
                'OriginalChinese:\n<<<ORIGINAL',
                originalText,
                '>>>',
                'Translation:\n<<<TRANSLATION',
                translationText,
                '>>>'
            ].join('\n');

            const start = Date.now();
            const raw = await requestAI(prompt);
            const secs = ((Date.now() - start) / 1000).toFixed(2);

            if (!raw) {
                reviewOutputChannel.appendLine('AI did not return a result.');
                reviewOutputChannel.appendLine(`\n[AI request time cost: ${secs} seconds]`);
                reviewOutputChannel.show(true);
                return;
            }

            function tryParseJSON(s: string): any | null {
                try { return JSON.parse(s); } catch { /* fallthrough */ }
                const m = s.match(/\{[\s\S]*\}/);
                if (m) { try { return JSON.parse(m[0]); } catch { return null; } }
                return null;
            }

            const parsed = tryParseJSON(raw) || { findings: [] };
            const findings: Array<{ original: string; translation: string; issue?: string; suggestion?: string; }> = Array.isArray(parsed.findings) ? parsed.findings : [];

            const transDecorations: vscode.DecorationOptions[] = [];
            const origDecorations: vscode.DecorationOptions[] = [];
            const buildHover = (f: { original: string; translation: string; issue?: string; suggestion?: string; }) => {
                const parts: string[] = [];
                if (f.issue) parts.push(`问题: ${f.issue}`);
                if (f.suggestion) parts.push(`建议: ${f.suggestion}`);
                const md = new vscode.MarkdownString(parts.join('\n\n'));
                md.isTrusted = true;
                return md;
            };

            reviewOutputChannel.appendLine('Translation Review Findings:');
            reviewOutputChannel.appendLine('----------------------------------------');

            if (findings.length === 0) {
                reviewOutputChannel.appendLine('未发现明显翻译问题。');
            }
            isHoverHighlightEnabled = false;
            const maxShow = Math.min(findings.length, 50);
            for (let i = 0; i < maxShow; i++) {
                const f = findings[i];
                if (!f || !f.translation || !f.original) continue;

                // Locate substrings in both editors
                const leftIdx = translationText.indexOf(f.translation);
                const rightIdx = originalText.indexOf(f.original);

                if (leftIdx >= 0) {
                    const startPos = translationEditor.document.positionAt(leftIdx);
                    const endPos = translationEditor.document.positionAt(leftIdx + f.translation.length);
                    transDecorations.push({ range: new vscode.Range(startPos, endPos), hoverMessage: buildHover(f) });
                }
                if (rightIdx >= 0) {
                    const startPos = originalEditor.document.positionAt(rightIdx);
                    const endPos = originalEditor.document.positionAt(rightIdx + f.original.length);
                    origDecorations.push({ range: new vscode.Range(startPos, endPos), hoverMessage: buildHover(f) });
                }

                // Print to output channel
                reviewOutputChannel.appendLine(`- 原文: ${f.original}`);
                reviewOutputChannel.appendLine(`  译文: ${f.translation}`);
                if (f.issue) reviewOutputChannel.appendLine(`  问题: ${f.issue}`);
                if (f.suggestion) reviewOutputChannel.appendLine(`  建议: ${f.suggestion}`);
                reviewOutputChannel.appendLine('');
            }





            reviewOutputChannel.appendLine('----------------------------------------');
            reviewOutputChannel.appendLine(`\n[AI request time cost: ${secs} seconds]`);

            // Apply highlights
            translationEditor.setDecorations(translationDecorationLeft, transDecorations);
            originalEditor.setDecorations(translationDecorationRight, origDecorations);

            // Show output
            reviewOutputChannel.show(true);

            // Build and save alignment map (.trans) for paragraph segments
            // try {
            //     await buildAndSaveAlignment(originalEditor, translationEditor);
            // } catch (e) {
            //     reviewOutputChannel.appendLine(`Alignment: unexpected error: ${e instanceof Error ? e.message : String(e)}`);
            // }

        });
    } catch (error) {


        vscode.window.showErrorMessage(`Failed to review translation: ${error instanceof Error ? error.message : String(error)}`);
    }
}

function get_piece(srcText: string, pos: number, max_length: number): [string, number, number] {
    let mod = 2;
    if (pos >= srcText.length) return ["", 0, 0];
    let end = pos + max_length;
    if (end >= srcText.length) {
        return [srcText.slice(pos), srcText.length - pos, 0];
    }
    while (end > pos && (srcText[end] !== '\n' || srcText[end - 1] !== '\n')) {
        end--;
    }
    if (end === pos) {
        while (end > pos && srcText[end] !== '\n') {
            end--;
        }
        mod = 1;
    }
    if (end === pos) {
        mod = 0;
        end = pos + max_length;
    }
    return [srcText.slice(pos, end).trimEnd(), end - pos, mod];
}

export async function translateToEnglish(): Promise<void> {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }
        const srcDoc = editor.document;
        const srcPath = srcDoc.uri.fsPath;
        const targetPath = get_en_file(srcPath);
        if (fs.existsSync(targetPath)) {
            vscode.window.showErrorMessage(`Target file already exists: ${targetPath}`);
            return;
        }

        const srcText = srcDoc.getText();
        reviewOutputChannel.clear();
        reviewOutputChannel.appendLine(`Translating: ${path.basename(srcPath)} -> ${path.basename(targetPath)}, total: ${srcText.length}`);
        reviewOutputChannel.show(true);

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Translating to English…',
            cancellable: true
        }, async () => {

            let prompt = [
                'You are a professional translator. Translate the following text into natural, accurate English.',
                'Requirements:',
                '- Preserve original paragraphs and code blocks/formatting.',
                '- Aim for one-to-one sentence alignment with the source when feasible; avoid merging/splitting sentences.',
                '- Output ONLY the translated plain text, no additional explanations or labels.',
                '- Keep the original text and format if do not translate especially at the end of text.',
                '- If the original line consisit of many " or ^, the length of output line shall be greater than last output line',  
                ''
            ].join('\n');

            let length = srcText.length;
            let pos = 0;
            let total_secs = 0;
            let this_prompt = '';
            while (length > 0 ) {
                let [this_text, this_length, mod] = get_piece(srcText, pos, 2000);
                const start = Date.now();
                reviewOutputChannel.append(`Starting at ${new Date().toLocaleString()} pos:${pos} length:${this_length}      `);
                reviewOutputChannel.show(true);
                //reviewOutputChannel.append(this_text);
                this_prompt = prompt + "\n" + this_text;
                let result = await requestAI(this_prompt);
                const secs = ((Date.now() - start) / 1000).toFixed(2);
                total_secs += Number(secs);
            
                if (!result) {
                    vscode.window.showErrorMessage('AI did not return a translation.');
                    return;
                }
                if (mod == 1)
                    result += '\n';
                else if ( mod == 2)
                    result += '\n\n';

                const data = Buffer.from(result, 'utf8');
                //reviewOutputChannel.appendLine('-------');
                //reviewOutputChannel.append(result);
                // Append the AI result to the target file instead of overwriting it
                await fs.promises.appendFile(targetPath, data);

                // Show and reveal the translated portion in the right editor, and scroll the left (source) editor
                try {
                    // Reveal source range in left editor
                    const srcStartPos = srcDoc.positionAt(pos);
                    const srcEndPos = srcDoc.positionAt(pos + this_length);
                    // Ensure left editor visible and reveal range
                    await vscode.window.showTextDocument(srcDoc, { viewColumn: vscode.ViewColumn.One, preview: false });
                    const leftEditorNow = vscode.window.visibleTextEditors.find(e => e.document === srcDoc);
                    if (leftEditorNow) {
                        leftEditorNow.revealRange(new vscode.Range(srcStartPos, srcEndPos), vscode.TextEditorRevealType.InCenter);
                    }

                    // Open the target document and reveal the newly appended text
                    const openedDoc = await vscode.workspace.openTextDocument(targetPath);
                    const rightEditorNow = await vscode.window.showTextDocument(openedDoc, { viewColumn: vscode.ViewColumn.Two, preview: false });
                    const rightText = openedDoc.getText();
                    const idx = rightText.lastIndexOf(result);
                    if (idx >= 0 && rightEditorNow) {
                        const rStart = openedDoc.positionAt(idx);
                        const rEnd = openedDoc.positionAt(idx + result.length);
                        rightEditorNow.revealRange(new vscode.Range(rStart, rEnd), vscode.TextEditorRevealType.InCenter);
                    }
                } catch (e) {
                    // ignore reveal errors
                }

                const doc = await vscode.workspace.openTextDocument(targetPath);
                await vscode.window.showTextDocument(doc, { viewColumn: vscode.ViewColumn.Two, preview: false });

                length -= this_length;
                pos += this_length;
                reviewOutputChannel.appendLine(`[AI request time cost: ${secs} seconds]`);
            }
            reviewOutputChannel.appendLine(`Saved: ${targetPath}`);
            reviewOutputChannel.appendLine(`Ending at ${new Date().toLocaleString()} `);
            reviewOutputChannel.appendLine(`[Total AI request time cost: ${total_secs} seconds]`);
            reviewOutputChannel.show(true);

            // Open in right window

            // // Build and save alignment map (.trans) now that both files exist
            // try {
            //     const destEditor = vscode.window.visibleTextEditors.find(e => e.document.uri.fsPath === targetPath) || vscode.window.activeTextEditor;
            //     if (destEditor && editor) {
            //         // Treat current editor as original, dest as translation for alignment
            //         await buildAndSaveAlignment(editor, destEditor);
            //     }
            // } catch (e) {
            //     reviewOutputChannel.appendLine(`Alignment after translation failed: ${e instanceof Error ? e.message : String(e)}`);
            // }

        });
    } catch (err) {
        vscode.window.showErrorMessage(`Failed to translate: ${err instanceof Error ? err.message : String(err)}`);
    }
}

export function registerTranslateLangCommands(context: vscode.ExtensionContext): void {
    const disposableReviewTranslation = vscode.commands.registerCommand('sourceseek.reviewTranslation', reviewTranslation);
    const disposableTranslateToEnglish = vscode.commands.registerCommand('sourceseek.translateToEnglish', translateToEnglish);
    const disposableReviewLastTranslation = vscode.commands.registerCommand('sourceseek.checkTranslation', checkTranslation);
    const disposableToggleHoverHighlight = vscode.commands.registerCommand('sourceseek.toggleHoverHighlight', toggleHoverHighlight);

    const hoverProvider = vscode.languages.registerHoverProvider({ scheme: 'file' }, {
        async provideHover(document, position) {
            if (!isHoverHighlightEnabled) {
                return undefined;
            }

            const curEditor = vscode.window.visibleTextEditors.find(e => e.document === document);
            if (!curEditor) return undefined;

            // Try alignment-based highlighting when hovering the original file
            // const alignment = await loadAlignmentForOriginal(document.uri.fsPath);
            // if (false ) {
            //     const otherEditor = vscode.window.visibleTextEditors.find(e => e.document.uri.fsPath === alignment.destPath);
            //     if (!otherEditor) return undefined; // don't auto-open on hover
            //     const line1 = position.line + 1;
            //     const seg = alignment.segments.find(s => line1 >= s.src_start_line && line1 <= s.src_end_line);
            //     if (!seg) return undefined;

            //     // Get ranges from cache if available
            //     const currentPath = document.uri.fsPath;
            //     const cached = fileRangesMap.get(currentPath);
            //     if (!cached) {
            //         // If not cached, compute and cache the ranges
            //         const curText = document.getText();
            //         const otherText = otherEditor.document.getText();
            //         const curRanges = splitSegmentWithOffsets(curText, hasChinese(curText));
            //         const otherRanges = splitSegmentWithOffsets(otherText, hasChinese(otherText));
            //         fileRangesMap.set(currentPath, { curRanges, otherRanges });
            //         fileRangesMap.set(otherEditor.document.uri.fsPath, { curRanges: otherRanges, otherRanges: curRanges });
            //     }
                
            //     const srcStart = new vscode.Position(seg.src_start_line - 1, 0);
            //     const srcEnd = new vscode.Position(seg.src_end_line - 1, curEditor.document.lineAt(seg.src_end_line - 1).range.end.character);
            //     curEditor.setDecorations(hoverDecorationCurrent, [{ range: new vscode.Range(srcStart, srcEnd) }]);

            //     const destStart = new vscode.Position(seg.dest_start_line - 1, 0);
            //     const destEnd = new vscode.Position(seg.dest_end_line - 1, otherEditor.document.lineAt(seg.dest_end_line - 1).range.end.character);
            //     otherEditor.setDecorations(hoverDecorationOther, [{ range: new vscode.Range(destStart, destEnd) }]);
                
            //     // Reveal the range in the other editor if it's not visible
            //     otherEditor.revealRange(new vscode.Range(destStart, destEnd), vscode.TextEditorRevealType.InCenterIfOutsideViewport);
            //     return undefined;
            // }

            // Fallback: sentence-index alignment with visible counterpart

            const activeColumn = curEditor.viewColumn;
            let otherEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.Two);
            if (activeColumn == vscode.ViewColumn.Two)
                otherEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.One);
            if (!otherEditor)
                return;
            // Get ranges from cache if available
            const currentPath = document.uri.fsPath;
            const otherPath = otherEditor.document.uri.fsPath;
            let cached = fileRangesMap.get(currentPath);
            if (!cached) {
                // If not cached, compute and cache the ranges
                const curText = document.getText();
                const otherText = otherEditor.document.getText();
                const curRanges = splitSegmentWithOffsets(curText, hasChinese(curText));
                const otherRanges = splitSegmentWithOffsets(otherText, hasChinese(otherText));
                fileRangesMap.set(currentPath, { curRanges, otherRanges });
                fileRangesMap.set(otherEditor.document.uri.fsPath, { curRanges: otherRanges, otherRanges: curRanges });
                cached = fileRangesMap.get(currentPath);
                if (!cached) return undefined;
            }
            
            
            const { curRanges, otherRanges } = cached;
            const offset = document.offsetAt(position);
            const idx = curRanges.findIndex((r: { start: number; end: number }) => offset >= r.start && offset < r.end);
            if (idx < 0) return undefined;

            const cr = curRanges[idx];
            const curRange = new vscode.Range(document.positionAt(cr.start), document.positionAt(cr.end));


            curEditor.setDecorations(hoverDecorationCurrent, [{ range: curRange }]);

            if (idx < otherRanges.length) {
                const or = otherRanges[idx];
                const otherRange = new vscode.Range(otherEditor.document.positionAt(or.start), otherEditor.document.positionAt(or.end));
                otherEditor.setDecorations(hoverDecorationOther, [{ range: otherRange }]);
                
                // Reveal the range in the other editor if it's not visible
                otherEditor.revealRange(otherRange, vscode.TextEditorRevealType.InCenterIfOutsideViewport);
            } else {
                otherEditor.setDecorations(hoverDecorationOther, []);
            }
            return undefined;
        }
    });

    context.subscriptions.push(disposableReviewTranslation);
    context.subscriptions.push(disposableTranslateToEnglish);
    context.subscriptions.push(disposableReviewLastTranslation);
    context.subscriptions.push(disposableToggleHoverHighlight);

    context.subscriptions.push(hoverProvider);
    
    // Listen for document open events to automatically check .rst files
    context.subscriptions.push(vscode.window.onDidChangeActiveTextEditor(async (editor) => {
        if (editor && editor.document.fileName.endsWith('.rst')) {
            // Wait a moment for the editor to be ready
            await new Promise(resolve => setTimeout(resolve, 100));
            await checkTranslation(false);
        }
    }));
}



export async function checkTranslation(force: boolean = true): Promise<void> {
    try {
        const editor = vscode.window.activeTextEditor;
        const activeViewColumn = editor?.viewColumn;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }
        
        const currentPath = editor.document.uri.fsPath;
        const currentText = editor.document.getText();
        const isCurrentChinese = hasChinese(currentText);
        const currentRanges = splitSegmentWithOffsets(currentText, isCurrentChinese);
        
        let otherPath: string | null = null;
        let otherText: string | null = null;
        let isOtherChinese: boolean = false;
        let otherRanges: Array<{ start: number; end: number }> = [];

        // Fallback to counterpart naming (_en suffix)
        let candidatePath = '';
        if (activeViewColumn == vscode.ViewColumn.One)
            candidatePath = finding_cores_file(currentPath);

        if (candidatePath != '') {
            const otherUri = vscode.Uri.file(candidatePath);
            const otherData = await vscode.workspace.fs.readFile(otherUri);
            otherText = Buffer.from(otherData).toString('utf8');
            isOtherChinese = hasChinese(otherText);
            otherRanges = splitSegmentWithOffsets(otherText, isOtherChinese);
            otherPath = candidatePath;
        } else {
            // If file does not exist, use right window editor as counterpart
            let otherEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.Two)
            if (activeViewColumn == vscode.ViewColumn.Two)
                otherEditor = vscode.window.visibleTextEditors.find(e => e.viewColumn === vscode.ViewColumn.One);


            if (force && otherEditor) {
                otherPath = otherEditor.document.uri.fsPath;
                otherText = otherEditor.document.getText();
                isOtherChinese = hasChinese(otherText);
                otherRanges = splitSegmentWithOffsets(otherText, isOtherChinese);
            } else {
                //vscode.window.showInfoMessage('Failed to load counterpart: ' + candidatePath);
                return;
            }
        }
        
        // Store both ranges in the global dictionary
        fileRangesMap.set(currentPath, { curRanges: currentRanges, otherRanges });
        fileRangesMap.set(otherPath, { curRanges: otherRanges, otherRanges: currentRanges });
        
        try {
            const doc = await vscode.workspace.openTextDocument(otherPath);
            let col = vscode.ViewColumn.Two;
            if (activeViewColumn == vscode.ViewColumn.One)
                await vscode.window.showTextDocument(doc, { viewColumn: col, preserveFocus: true });
            
            const hoverStatus = isHoverHighlightEnabled ? 'enabled' : 'disabled';
            vscode.window.setStatusBarMessage(`Review Last Translation: loaded counterpart (hover highlight ${hoverStatus})`, 3000);
            isHoverHighlightEnabled = true;
        } catch (e) {
            vscode.window.showErrorMessage(`Failed to open counterpart: ${e instanceof Error ? e.message : String(e)}`);
        }
    } catch (e) {
        vscode.window.showErrorMessage(`Review Last Translation failed: ${e instanceof Error ? e.message : String(e)}`);
    }
}

// Toggle hover highlight feature
export async function toggleHoverHighlight(): Promise<void> {
    isHoverHighlightEnabled = !isHoverHighlightEnabled;
    const status = isHoverHighlightEnabled ? 'enabled' : 'disabled';
    vscode.window.showInformationMessage(`Translation Hover Highlight ${status}`);
    
    // Clear any existing hover decorations when disabling
    if (!isHoverHighlightEnabled) {
        vscode.window.visibleTextEditors.forEach(editor => {
            editor.setDecorations(hoverDecorationCurrent, []);
            editor.setDecorations(hoverDecorationOther, []);
        });
    }
}
