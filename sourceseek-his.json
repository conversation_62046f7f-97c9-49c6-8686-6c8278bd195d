[{"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "registerSymbolPreviewView+1133", "kind": 11, "detail": "", "uri": "file:///src/SymbolPreviewView.ts", "range": {"start": {"line": 1132, "character": 48}, "end": {"line": 1132, "character": 48}}, "selectionRange": {"start": {"line": 1132, "character": 48}, "end": {"line": 1132, "character": 48}}}]}, {"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "SymbolPreviewView.ts+1029", "kind": 20, "detail": "", "uri": "file:///src/SymbolPreviewView.ts", "range": {"start": {"line": 1028, "character": 71}, "end": {"line": 1028, "character": 71}}, "selectionRange": {"start": {"line": 1028, "character": 71}, "end": {"line": 1028, "character": 71}}}]}, {"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "CallHierarchyProvider.ts+271", "kind": 20, "detail": "", "uri": "file:///src/CallHierarchyProvider.ts", "range": {"start": {"line": 270, "character": 32}, "end": {"line": 270, "character": 32}}, "selectionRange": {"start": {"line": 270, "character": 32}, "end": {"line": 270, "character": 32}}}]}, {"size": 1, "description": "", "items": [{"name": "getSymbolKind", "kind": 11, "detail": "709;\"", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 708, "character": 0}, "end": {"line": 708, "character": 0}}, "selectionRange": {"start": {"line": 708, "character": 0}, "end": {"line": 708, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "getSymbolInfo", "kind": 7, "detail": "CCallHierarchyProvider", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 383, "character": 0}, "end": {"line": 383, "character": 0}}, "selectionRange": {"start": {"line": 383, "character": 0}, "end": {"line": 383, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "readtagsGoToSymbolInWorkspaceCommand", "detail": "9;\"", "uri": "file:///src/globalSettings.ts", "head": false, "range": {"start": {"line": 8, "character": 0}, "end": {"line": 8, "character": 0}}, "selectionRange": {"start": {"line": 8, "character": 0}, "end": {"line": 8, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "ctagsGoToSymbolInEditorCommand", "detail": "13;\"", "uri": "file:///src/globalSettings.ts", "head": false, "range": {"start": {"line": 12, "character": 0}, "end": {"line": 12, "character": 0}}, "selectionRange": {"start": {"line": 12, "character": 0}, "end": {"line": 12, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "findField", "kind": 11, "detail": "200;\"", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 199, "character": 0}, "end": {"line": 199, "character": 0}}, "selectionRange": {"start": {"line": 199, "character": 0}, "end": {"line": 199, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "definitionToSymbolInformation", "kind": 11, "detail": "139;\"", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 138, "character": 0}, "end": {"line": 138, "character": 0}}, "selectionRange": {"start": {"line": 138, "character": 0}, "end": {"line": 138, "character": 0}}}]}, {"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "ReadTags.ts+202", "kind": 20, "detail": "", "uri": "file:///src/ReadTags.ts", "range": {"start": {"line": 201, "character": 10}, "end": {"line": 201, "character": 10}}, "selectionRange": {"start": {"line": 201, "character": 10}, "end": {"line": 201, "character": 10}}}]}, {"size": 1, "description": "", "items": [{"name": "functionDeclarationRegex", "detail": "692;\"", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 691, "character": 0}, "end": {"line": 691, "character": 0}}, "selectionRange": {"start": {"line": 691, "character": 0}, "end": {"line": 691, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "findFunctionNameByLine", "kind": 11, "detail": "310;\"", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 309, "character": 0}, "end": {"line": 309, "character": 0}}, "selectionRange": {"start": {"line": 309, "character": 0}, "end": {"line": 309, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "getFunctionNameAndStartLine", "kind": 11, "detail": "320;\"", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 319, "character": 0}, "end": {"line": 319, "character": 0}}, "selectionRange": {"start": {"line": 319, "character": 0}, "end": {"line": 319, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "Number", "detail": "735;\"", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 734, "character": 0}, "end": {"line": 734, "character": 0}}, "selectionRange": {"start": {"line": 734, "character": 0}, "end": {"line": 734, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "findFunctionReferences", "kind": 11, "detail": "619;\"", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 618, "character": 0}, "end": {"line": 618, "character": 0}}, "selectionRange": {"start": {"line": 618, "character": 0}, "end": {"line": 618, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "convertToFuncRefInfo", "kind": 7, "detail": "SymbolInfo", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 112, "character": 0}, "end": {"line": 112, "character": 0}}, "selectionRange": {"start": {"line": 112, "character": 0}, "end": {"line": 112, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "g_readtagThreads", "kind": 12, "detail": "", "uri": "file:///src/ReadTags.ts", "head": false, "range": {"start": {"line": 26, "character": 0}, "end": {"line": 26, "character": 0}}, "selectionRange": {"start": {"line": 26, "character": 0}, "end": {"line": 26, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "getDatabasePath", "kind": 11, "detail": "", "uri": "file:///src/BuildDatabase.ts", "head": false, "range": {"start": {"line": 35, "character": 0}, "end": {"line": 35, "character": 0}}, "selectionRange": {"start": {"line": 35, "character": 0}, "end": {"line": 35, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "get<PERSON><PERSON>mand<PERSON><PERSON>", "kind": 11, "detail": "", "uri": "file:///src/BuildDatabase.ts", "head": false, "range": {"start": {"line": 15, "character": 0}, "end": {"line": 15, "character": 0}}, "selectionRange": {"start": {"line": 15, "character": 0}, "end": {"line": 15, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "g_functionPointerMap", "kind": 12, "detail": "", "uri": "file:///src/extension.ts", "head": false, "range": {"start": {"line": 32, "character": 0}, "end": {"line": 32, "character": 0}}, "selectionRange": {"start": {"line": 32, "character": 0}, "end": {"line": 32, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "BrowserHistory", "detail": "", "uri": "file:///src/BrowserHistoryStore.ts", "head": false, "range": {"start": {"line": 43, "character": 0}, "end": {"line": 43, "character": 0}}, "selectionRange": {"start": {"line": 43, "character": 0}, "end": {"line": 43, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "openai", "detail": "", "uri": "file:///src/ReviewCode.ts", "head": false, "range": {"start": {"line": 10, "character": 0}, "end": {"line": 10, "character": 0}}, "selectionRange": {"start": {"line": 10, "character": 0}, "end": {"line": 10, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "filterSymbolInfos", "kind": 11, "detail": "", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 588, "character": 0}, "end": {"line": 588, "character": 0}}, "selectionRange": {"start": {"line": 588, "character": 0}, "end": {"line": 588, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "refreshDocumentSymbols", "kind": 11, "detail": "", "uri": "file:///src/EditWindow.ts", "head": false, "range": {"start": {"line": 25, "character": 0}, "end": {"line": 25, "character": 0}}, "selectionRange": {"start": {"line": 25, "character": 0}, "end": {"line": 25, "character": 0}}}]}, {"size": 11, "description": "", "items": [{"name": "document", "detail": "", "uri": "file:///src/CallHierarchyProvider.ts", "head": false, "range": {"start": {"line": 339, "character": 0}, "end": {"line": 339, "character": 0}}, "selectionRange": {"start": {"line": 339, "character": 0}, "end": {"line": 339, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/CommentToggle.ts", "head": false, "range": {"start": {"line": 22, "character": 0}, "end": {"line": 22, "character": 0}}, "selectionRange": {"start": {"line": 22, "character": 0}, "end": {"line": 22, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/EditWindow.ts", "head": false, "range": {"start": {"line": 637, "character": 0}, "end": {"line": 637, "character": 0}}, "selectionRange": {"start": {"line": 637, "character": 0}, "end": {"line": 637, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/EditWindow.ts", "head": false, "range": {"start": {"line": 730, "character": 0}, "end": {"line": 730, "character": 0}}, "selectionRange": {"start": {"line": 730, "character": 0}, "end": {"line": 730, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/EditWindow.ts", "head": false, "range": {"start": {"line": 808, "character": 0}, "end": {"line": 808, "character": 0}}, "selectionRange": {"start": {"line": 808, "character": 0}, "end": {"line": 808, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/ExportCallHierarchy.ts", "head": false, "range": {"start": {"line": 53, "character": 0}, "end": {"line": 53, "character": 0}}, "selectionRange": {"start": {"line": 53, "character": 0}, "end": {"line": 53, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/MacroDefinition.ts", "head": false, "range": {"start": {"line": 552, "character": 0}, "end": {"line": 552, "character": 0}}, "selectionRange": {"start": {"line": 552, "character": 0}, "end": {"line": 552, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/SymbolPreviewView.ts", "head": false, "range": {"start": {"line": 1014, "character": 0}, "end": {"line": 1014, "character": 0}}, "selectionRange": {"start": {"line": 1014, "character": 0}, "end": {"line": 1014, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/SymbolPreviewView.ts", "head": false, "range": {"start": {"line": 1213, "character": 0}, "end": {"line": 1213, "character": 0}}, "selectionRange": {"start": {"line": 1213, "character": 0}, "end": {"line": 1213, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/SymbolPreviewView.ts", "head": false, "range": {"start": {"line": 338, "character": 0}, "end": {"line": 338, "character": 0}}, "selectionRange": {"start": {"line": 338, "character": 0}, "end": {"line": 338, "character": 0}}}, {"name": "document", "detail": "", "uri": "file:///src/UtilFuns.ts", "head": false, "range": {"start": {"line": 529, "character": 0}, "end": {"line": 529, "character": 0}}, "selectionRange": {"start": {"line": 529, "character": 0}, "end": {"line": 529, "character": 0}}}]}]