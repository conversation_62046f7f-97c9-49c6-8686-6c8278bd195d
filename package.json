{"name": "SourceSeek", "displayname": "SourceSeek", "description": "SourceSeek is a vscode extension that provides c/c++ code search and navigation features.", "version": "1.8.7", "publisher": "lingxf", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/lingxf/sourceseek"}, "engines": {"vscode": "^1.60.0"}, "categories": ["Programming Languages"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"views": {"explorer": [{"id": "browserHistoryView", "name": "Symbol History", "icon": "$(ungroup-by-ref-type)"}]}, "commands": [{"command": "functionTreeView.refresh", "title": "Refresh Function Tree View", "icon": "$(refresh)"}, {"command": "browserHistoryView.refresh", "title": "Refresh Symbol History", "icon": "$(refresh)"}, {"command": "browserHistoryView.clear", "title": "Clear Symbol History", "icon": "$(trash)"}, {"command": "browserHistoryView.removeItem", "title": "Remove from History", "icon": "$(close)"}, {"command": "browserHistoryView.exportToClipboard", "title": "Export to Clipboard", "icon": "$(copy)"}, {"command": "browserHistoryView.toggleFunctionOnly", "title": "Toggle Function Only Mode", "icon": "$(filter)", "tooltip": "function only mode: ${browserHistoryView.functiononlymode ? 'ON' : 'OFF'}"}, {"command": "sourceseek.toggleFunctionOnlyFilter", "title": "Toggle Function-Only Filter", "icon": "$(filter)"}, {"command": "browserHistoryView.saveToFile", "title": "Save History to File", "icon": "$(save)", "category": "SourceSeek"}, {"command": "browserHistoryView.loadFromFile", "title": "Load History from File", "icon": "$(folder-opened)", "category": "SourceSeek"}, {"command": "browserHistoryView.reverseOrder", "title": "Reverse History Order", "icon": "$(sort-precedence)", "tooltip": "${browserHistoryView._newestFirst ? 'Newest First' : 'Oldest First'}", "category": "SourceSeek"}, {"command": "browserHistoryView.openSettings", "title": "Open Settings", "icon": "$(gear)", "category": "SourceSeek"}, {"command": "sourceseek.searchSymbol", "title": "Search Symbol in Workspace", "icon": "$(search)", "category": "SourceSeek"}, {"command": "sourceseek.buildDatabase", "title": "Build Code Database", "icon": "$(database)", "category": "SourceSeek"}, {"command": "sourceseek.collectCscopeFiles", "title": "Generage Kernel Fileslist", "icon": "$(file-binary)", "category": "SourceSeek"}, {"command": "sourceseek.showConfigDialog", "title": "Create Linux Kernel Index", "icon": "$(gear)", "category": "SourceSeek"}, {"command": "sourceseek.loadMacroDefinition", "title": "Load Macro Definition File", "icon": "$(database)", "category": "SourceSeek"}, {"command": "sourceseek.toggleNonActiveCodeAsGrey", "title": "Toggle non-active Code Highlighting", "icon": "$(eye)", "category": "SourceSeek"}, {"command": "sourceseek.showMacroDefinitionFile", "title": "Show Macro Definition File", "icon": "$(file-code)", "category": "SourceSeek"}, {"command": "browserHistoryView.editDescription", "title": "Edit Description", "icon": "$(edit)", "category": "SourceSeek"}, {"command": "sourceseek.linkToDefinition", "title": "Link Definition/<PERSON>", "icon": "$(link)", "category": "SourceSeek"}, {"command": "sourceseek.linkToCaller", "title": "<PERSON>/Mark Definition", "icon": "$(link)", "category": "SourceSeek"}, {"command": "sourceseek.printFunctionPointerMap", "title": "Print Function Pointer Map", "category": "SourceSeek"}, {"command": "browserHistoryView.saveFunctionPointerMap", "title": "Save Function Pointer Map", "icon": "$(save-as)", "category": "SourceSeek"}, {"command": "browserHistoryView.loadFunctionPointerMap", "title": "Load Function Pointer Map", "icon": "$(cloud-download)", "category": "SourceSeek"}, {"command": "browserHistoryView.clearFunctionPointerMap", "title": "Clear Function Pointer Map", "icon": "$(clear-all)", "category": "SourceSeek"}, {"command": "sourceseek.toggleNonStatic", "title": "Show Non-static Function", "icon": "$(filter-filled)", "category": "SourceSeek"}, {"command": "browserHistoryView.openInRightWindow", "title": "Open in Right Window", "icon": "$(split-horizontal)", "category": "SourceSeek"}, {"command": "sourceseek.openInRightWindow", "title": "Open in Right Window", "icon": "$(split-horizontal)", "category": "SourceSeek"}, {"command": "sourceseek.markBootmark", "title": "<PERSON>", "icon": "$(split-horizontal)", "category": "SourceSeek"}, {"command": "sourceseek.reviewDiffs", "title": "Review File Changes", "icon": "$(diff)", "category": "SourceSeek"}, {"command": "sourceseek.reviewLastCommit", "title": "Review Last File Commit", "icon": "$(git-commit)", "category": "SourceSeek"}, {"command": "sourceseek.reviewCurrentFile", "title": "Review Current File", "icon": "$(file-code)", "category": "SourceSeek"}, {"command": "sourceseek.reviewCurrentFileWithCustomRule", "title": "Review File with Custom Rules", "icon": "$(checklist)", "category": "SourceSeek"}, {"command": "sourceseek.reviewWholeGitDiff", "title": "Review Whole Git Diff", "icon": "$(git-commit)", "category": "SourceSeek"}, {"command": "sourceseek.reviewLastGitCommit", "title": "Review Last Git Commit", "icon": "$(git-commit)", "category": "SourceSeek"}, {"command": "sourceseek.reviewCurrentFunction", "title": "Review Current Function", "icon": "$(symbol-method)", "category": "SourceSeek"}, {"command": "sourceseek.translateToEnglish", "title": "Translate to English", "icon": "$(globe)", "category": "SourceSeek"}, {"command": "sourceseek.checkTranslation", "title": "Check Translation", "icon": "$(history)", "category": "SourceSeek"}, {"command": "sourceseek.toggleHoverHighlight", "title": "Toggle Translation Highlight", "icon": "$(history)", "category": "SourceSeek"}, {"command": "sourceseek.reviewTranslation", "title": "Review Translation", "icon": "$(check)", "category": "SourceSeek"}, {"command": "sourceseek.openRule", "title": "Open Rule File", "icon": "$(book)", "category": "SourceSeek"}, {"command": "sourceseek.exportCallHierarchy", "title": "Export Call Hierarchy", "icon": "$(export)", "category": "SourceSeek"}, {"command": "sourceseek.manageCustomMacros", "title": "Manage Custom Macros", "icon": "$(symbol-constant)", "category": "sourceseek"}, {"command": "sourceseek.toggleComment", "title": "Toggle Comment", "icon": "$(comment)", "category": "SourceSeek"}, {"command": "sourceseek.coverityViolation", "title": "Coverity Violation", "icon": "$(warning)", "category": "SourceSeek"}, {"command": "sourceseek.findAssignment", "title": "Find Assignment", "icon": "$(comment)", "category": "SourceSeek"}, {"command": "sourceseek.about", "title": "About SourceSeek", "icon": "$(info)", "category": "SourceSeek"}, {"command": "sourceseek.selectAIModel", "title": "Select AI Model", "icon": "$(robot)", "category": "SourceSeek"}, {"command": "sourceseek.findSymbol", "title": "Grep Symbol", "icon": "$(comment)", "category": "SourceSeek"}, {"command": "sourceseek.gotoFile", "title": "Go to File", "icon": "$(comment)", "category": "SourceSeek"}, {"command": "sourceseek.findInclude", "title": "Find Files Including This", "icon": "$(comment)", "category": "SourceSeek"}, {"command": "sourceseek.showCaller", "title": "Show Caller", "icon": "$(graph)", "category": "SourceSeek"}, {"command": "sourceseek.toggleExtraHighlight", "title": "Toggle Extra Highlighting", "icon": "$(symbol-color)", "category": "SourceSeek"}], "menus": {"view/title": [{"command": "browserHistoryView.refresh", "when": "view == browserHistoryView", "group": "1_history@1"}, {"command": "browserHistoryView.toggleFunctionOnly", "when": "view == browserHistoryView", "group": "navigation"}, {"command": "sourceseek.toggleFunctionOnlyFilter", "when": "view == outline", "group": "navigation"}, {"command": "browserHistoryView.saveToFile", "when": "view == browserHistoryView", "group": "navigation"}, {"command": "browserHistoryView.loadFromFile", "when": "view == browserHistoryView", "group": "navigation"}, {"command": "browserHistoryView.clear", "when": "view == browserHistoryView", "group": "navigation"}, {"command": "browserHistoryView.reverseOrder", "when": "view == browserHistoryView", "group": "1_history@2"}, {"command": "browserHistoryView.openSettings", "when": "view == browserHistoryView", "group": "navigation"}, {"command": "sourceseek.buildDatabase", "when": "view == browserHistoryView", "group": "3_build@1"}, {"command": "sourceseek.loadMacroDefinition", "when": "view == browserHistoryView", "group": "3_build@2"}, {"command": "sourceseek.toggleNonActiveCodeAsGrey", "when": "view == browserHistoryView", "group": "3_build@3"}, {"command": "sourceseek.showMacroDefinitionFile", "when": "view == browserHistoryView", "group": "3_build@4"}, {"command": "sourceseek.toggleExtraHighlight", "when": "view == browserHistoryView", "group": "3_build@5"}, {"submenu": "sourceseek.kernel.menu", "when": "view == browserHistoryView", "group": "3_build@5"}, {"command": "browserHistoryView.saveFunctionPointerMap", "when": "view == browserHistoryView", "group": "3_build@6"}, {"command": "browserHistoryView.loadFunctionPointerMap", "when": "view == browserHistoryView", "group": "3_build@7"}, {"command": "browserHistoryView.clearFunctionPointerMap", "when": "view == browserHistoryView", "group": "3_build@8"}, {"command": "sourceseek.toggleNonStatic", "when": "view == outline", "group": "navigation"}, {"command": "sourceseek.exportCallHierarchy", "when": "view == references:callers", "group": "navigation"}, {"command": "sourceseek.manageCustomMacros", "when": "view == browserHistoryView", "group": "3_build@3"}], "view/item/context": [{"command": "browserHistoryView.removeItem", "when": "view == browserHistoryView", "group": "inline"}, {"command": "browserHistoryView.exportToClipboard", "when": "view == browserHistoryView", "group": "1_modification@2"}, {"command": "browserHistoryView.editDescription", "when": "view == browserHistoryView", "group": "1_modification@3"}, {"command": "browserHistoryView.openInRightWindow", "when": "view == browserHistoryView", "group": "1_modification@1"}], "references-view/title": [{"command": "sourceseek.exportCallHierarchy", "group": "navigation"}], "callHierarchy/title": [{"command": "sourceseek.exportCallHierarchy", "group": "navigation"}], "references-view.callHierarchy/title": [{"command": "sourceseek.exportCallHierarchy", "group": "navigation"}], "editor/context": [{"command": "sourceseek.toggleComment", "when": "editorHasSelection && editorTextFocus", "group": "1_modification@1"}, {"command": "sourceseek.coverityViolation", "when": "editorTextFocus", "group": "1_modification@2"}, {"command": "sourceseek.searchSymbol", "when": "editorHasSelection && editorTextFocus", "group": "navigation@1"}, {"command": "sourceseek.linkToDefinition", "when": "editorHasSelection && editorTextFocus", "group": "navigation@2"}, {"command": "sourceseek.linkToCaller", "when": "editorHasSelection && editorTextFocus", "group": "navigation@3"}, {"command": "sourceseek.printFunctionPointerMap", "when": "editorTextFocus", "group": "navigation@5"}, {"command": "sourceseek.openInRightWindow", "when": "editorTextFocus", "group": "navigation@4"}, {"submenu": "sourceseek.review.menu", "when": "editorTextFocus", "group": "navigation@6"}, {"submenu": "sourceseek.context.menu", "when": "editorTextFocus", "group": "navigation@7"}, {"command": "sourceseek.showCaller", "when": "editorTextFocus", "group": "navigation@8"}], "editor/title": [{"submenu": "sourceseek.kernel.menu", "group": "navigation"}, {"command": "sourceseek.buildDatabase", "group": "navigation@1"}, {"command": "sourceseek.loadMacroDefinition", "group": "navigation@2"}, {"command": "sourceseek.toggleNonActiveCodeAsGrey", "group": "navigation@3"}, {"command": "sourceseek.showMacroDefinitionFile", "group": "navigation@4"}, {"command": "sourceseek.manageCustomMacros", "group": "navigation@3"}], "editor/title/context": [{"submenu": "sourceseek.kernel.menu", "group": "navigation"}, {"command": "sourceseek.buildDatabase", "group": "navigation@1"}, {"command": "sourceseek.loadMacroDefinition", "group": "navigation@2"}, {"command": "sourceseek.toggleNonActiveCodeAsGrey", "group": "navigation@3"}, {"command": "sourceseek.showMacroDefinitionFile", "group": "navigation@4"}, {"command": "sourceseek.manageCustomMacros", "group": "navigation@3"}], "sourceseek.review.menu": [{"command": "sourceseek.reviewDiffs", "group": "review"}, {"command": "sourceseek.reviewLastCommit", "group": "review"}, {"command": "sourceseek.reviewCurrentFile", "group": "review"}, {"command": "sourceseek.reviewCurrentFileWithCustomRule", "group": "review"}, {"command": "sourceseek.reviewWholeGitDiff", "group": "review"}, {"command": "sourceseek.reviewLastGitCommit", "group": "review"}, {"command": "sourceseek.reviewCurrentFunction", "group": "review"}, {"command": "sourceseek.reviewTranslation", "group": "trans@1"}, {"command": "sourceseek.translateToEnglish", "group": "trans@2"}, {"command": "sourceseek.checkTranslation", "group": "trans@3"}, {"command": "sourceseek.toggleHoverHighlight", "group": "trans@4"}, {"command": "sourceseek.openRule", "group": "review"}], "sourceseek.kernel.menu": [{"command": "sourceseek.showConfigDialog", "group": "kernel@1"}, {"command": "sourceseek.collectCscopeFiles", "group": "kernel@2"}], "sourceseek.context.menu": [{"command": "sourceseek.markBootmark", "title": "<PERSON>", "group": "navigation@4"}, {"command": "sourceseek.findAssignment", "title": "Find Assignment", "group": "navigation@0"}, {"command": "sourceseek.findSymbol", "title": "Find Symbol", "group": "navigation@1"}, {"command": "sourceseek.gotoFile", "title": "Go to File", "group": "navigation@2"}, {"command": "sourceseek.findInclude", "group": "navigation@3"}, {"command": "sourceseek.buildDatabase", "group": "3_build@1"}, {"command": "sourceseek.loadMacroDefinition", "group": "3_build@2"}, {"command": "sourceseek.manageCustomMacros", "group": "3_build@3"}, {"command": "sourceseek.toggleNonActiveCodeAsGrey", "group": "3_build@4"}, {"command": "sourceseek.toggleExtraHighlight", "group": "3_build@5"}, {"command": "sourceseek.showMacroDefinitionFile", "group": "3_build@5"}, {"submenu": "sourceseek.kernel.menu", "group": "3_build@6"}, {"command": "sourceseek.about", "group": "4_about@1"}], "menuBar": [{"submenu": "sourceseek.toplevel", "title": "SourceSeek", "when": "true"}], "sourceseek.toplevel": [{"command": "sourceseek.buildDatabase", "when": "true", "group": "1_build"}, {"command": "sourceseek.loadMacroDefinition", "when": "true", "group": "1_build"}]}, "submenus": [{"id": "sourceseek.review.menu", "label": "Review"}, {"id": "sourceseek.kernel.menu", "label": "<PERSON><PERSON>"}, {"id": "sourceseek.toplevel", "label": "SourceSeek"}, {"id": "sourceseek.context.menu", "label": "SourceSeek"}], "configuration": {"type": "object", "title": "SourceSeek ", "properties": {"sourceseek.enableSourceSeek": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable SourceSeek."}, "sourceseek.enableReferenceSearch": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable CScope Reference Search."}, "sourceseek.enableDefinitionSearch": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable Ctags Jump to Definition."}, "sourceseek.enableCallHierarchy": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable CallHierarchy Search."}, "sourceseek.enableWorkspaceSymbol": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable Workspace Symbols Search."}, "sourceseek.enableDocumentSymbol": {"type": "boolean", "default": true, "scope": "language-overridable", "description": "Enable Document Symbols Search."}, "sourceseek.autoBuildCtagsDatabase": {"type": "boolean", "default": false, "description": "Automatically build ctags database when missing"}, "sourceseek.autoBuildCscopeDatabase": {"type": "boolean", "default": false, "description": "Automatically build cscope database when missing"}, "sourceseek.hoverPreviewEnabled": {"type": "boolean", "default": true, "description": "Enable preview when mouse hovering over symbols"}, "sourceseek.openRightWindow": {"type": "boolean", "default": true, "description": "Open symbol definitions in a right window instead of using hover tips"}, "sourceseek.openInNewWindow": {"type": "boolean", "default": false, "description": "Open symbol definitions in a new window instead of reusing the same tab"}, "sourceseek.hoverAddHistory": {"type": "boolean", "default": false, "description": "Add symbols to history when hovering"}, "sourceseek.callhierarchyAddHistory": {"type": "boolean", "default": true, "description": "Add symbols to history when hovering"}, "sourceseek.useInternalExecutable": {"type": "boolean", "default": true, "description": "Use cscope/ctags command included in the extension(both Windows/Linux). If false, it will use the executable path specified in sourceseek.executablePath, or use the executable path of VSCode. If internal command not work, you can disable this option and use your own command, put cscope/ctags/readtags in your PATH."}, "sourceseek.executablePath": {"type": "string", "default": "", "scope": "language-overridable", "description": "Path of executables folder, by default it will use cscope/ctags/readtags from extension folder, or you can specify the path for these command, they shall be in the same folder."}, "sourceseek.showCommand": {"type": "boolean", "default": false, "description": "Print cscope/ctags/readtags command before execute, but command for preview is not shown."}, "sourceseek.databasePath": {"type": "string", "default": ".", "description": "IMPORTANT:Pls only configure this in workspace setting, this is the path of database, start from ${workspaceRoot}., default is in the workspace root, you can put it in .vscode"}, "sourceseek.cscope_database": {"type": "string", "default": "cscope.out", "description": "The name of cscope database."}, "sourceseek.ctags_database": {"type": "string", "default": "tags", "description": "The name of ctags database."}, "sourceseek.excludedPaths": {"type": "array", "default": [], "description": " IMPORTANT:Pls only configure this in workspace setting!!! Rules to exclude files. Each entry shall be a regular expression. If the file paths matches the rule it would be removed from source file list when database is built. For example rule '/exclude/.*' will exclude any file under filder '/exclude/'."}, "sourceseek.documentSelector": {"default": {"scheme": "file"}, "description": "Document selector object used when registering symbol providers, read more at https://code.visualstudio.com/api/references/vscode-api#DocumentSelector."}, "sourceseek.fnstructOnly": {"type": "boolean", "default": false, "description": "Show only function structure without implementation details"}, "sourceseek.checkSymbolKind": {"type": "boolean", "default": false, "description": "check symbol kinds in call hierarchy"}, "sourceseek.maxHistorySize": {"type": "number", "default": 100, "minimum": 50, "maximum": 1000, "description": "Maximum number of entries to keep in the symbol history"}, "sourceseek.nonStaticTitle": {"type": "string", "default": "Show Non-Static Only"}, "sourceseek.hoverCallerEnabled": {"type": "boolean", "default": false, "description": "Enable automatically show call hierarchy when mouse hover a symbol"}, "sourceseek.hoverCallerClickTrigger": {"type": "boolean", "default": false, "description": "Enable automatically show call hierarchy when mouse hover a symbol"}, "sourceseek.customRuleFile": {"type": "string", "default": "", "description": "Path to a custom rule file for code review. If not specified, the built-in rules will be used."}, "sourceseek.aiModel": {"type": "string", "default": "DeepSeek-V3", "enum": ["DeepSeek-V3", "Qwen3-Coder-480B-A35B-Instruct", "GLM-4.5", "Kimi-K2-Instruct"], "enumDescriptions": ["DeepSeek V3 model", "Qwen3 Coder 480B model", "GLM 4.5 model", "Kimi K2 Instruct model"], "description": "Select the AI model to use for code review and analysis."}, "sourceseek.aiTemperature": {"type": "number", "default": 0.2, "minimum": 0, "maximum": 1, "description": "Temperature for AI model, range from 0.0 to 1.0, higher value means more random, lower value means more deterministic."}, "sourceseek.macroDefinitionFilePath": {"type": "string", "default": "", "description": "Path to the macro definition file. This file will be loaded automatically when the workspace is opened."}, "sourceseek.kernelOutputPath": {"type": "string", "default": "", "description": "Path to the kernel output folder. This is used for collecting cscope files."}, "sourceseek.cscopeFilesPath": {"type": "string", "default": "", "description": "Path to the folder where cscope.files will be generated."}, "sourceseek.commentFormat": {"type": "string", "enum": ["//", "/* */"], "default": "//", "description": "Comment format to use when commenting/uncommenting code lines. Choose between '//' for single-line comments or '/* */' for block comments."}}}, "keybindings": [{"command": "sourceseek.isMiddleClick", "key": "mouse3", "when": "editorTextFocus"}, {"command": "sourceseek.markBootmark", "key": "F2", "when": "editorTextFocus"}, {"command": "sourceseek.markBootmark", "key": "ctrl+alt+d", "when": "editorTextFocus"}, {"command": "sourceseek.findAssignment", "key": "ctrl+alt+a", "when": "editorTextFocus"}, {"command": "sourceseek.coverityViolation", "key": "ctrl+shift+e", "when": "editorTextFocus"}]}, "scripts": {"vscode:prepublish": "echo #npm run compile", "compile": "echo #tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/babel__traverse": "^7.20.6", "@types/glob": "^7.1.3", "@types/lodash": "^4.17.16", "@types/node": "14.x", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "eslint": "^7.27.0", "glob": "^7.1.7", "lodash": "^4.17.21", "mocha": "^8.4.0", "typescript": "^4.3.2", "vscode-test": "^1.5.2"}, "dependencies": {"openai": "^4.96.0"}, "json.sortKeys": false}