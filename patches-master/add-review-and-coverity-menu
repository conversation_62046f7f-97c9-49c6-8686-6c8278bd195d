Add review and coverity menu and rules

From: <PERSON><PERSON> Ling <<EMAIL>>

This reverts commit 1077a6e6a560f95c4886670c26adb1c19066501c.
---
 coverity_exception.rules |   99 ++++++++++++
 package.json             |   10 +
 rules/c_rules.md         |  365 ++++++++++++++++++++++++++++++++++++++++++++++
 rules/c_rules_kernel.md  |  103 +++++++++++++
 rules/common_rules.md    |   86 +++++++++++
 rules/log_rules.md       |  267 ++++++++++++++++++++++++++++++++++
 rules/log_rules_amend.md |  132 +++++++++++++++++
 rules/python_rules.md    |  205 ++++++++++++++++++++++++++
 rules/shell_rules.md     |  157 ++++++++++++++++++++
 9 files changed, 1424 insertions(+)
 create mode 100644 coverity_exception.rules
 create mode 100644 rules/c_rules.md
 create mode 100644 rules/c_rules_kernel.md
 create mode 100644 rules/common_rules.md
 create mode 100644 rules/log_rules.md
 create mode 100644 rules/log_rules_amend.md
 create mode 100644 rules/python_rules.md
 create mode 100644 rules/shell_rules.md

diff --git a/coverity_exception.rules b/coverity_exception.rules
new file mode 100644
index 0000000..db9ec3e
--- /dev/null
+++ b/coverity_exception.rules
@@ -0,0 +1,99 @@
+2025-07-15 17:00:00
+SYSSW_V_2.1_01	不得包含不可达代码(unreachable code)	正常情况下代码中不应该有永不可达的代码(这个例外列举仅对特殊的底层系统代码 - 重启函数软件处理完必要的资源释放后等待硬件重启软件;之后的代码执行不到，也不需要执行，形式上符合函数style要求，有return)
+SYSSW_V_2.2_01	Un10	MCU临界区代码采用统一的Schm_XXX的形式，函数的内容由客户通过RTE填充
+SYSSW_V_4.12_01	不应使用动态内存分配。	初始化时分配内存
+SYSSW_V_4.12_02	不应使用动态内存分配。	只能运行时确定的内存大小
+SYSSW_V_5.1_01	vocf	调用第三方的宏生成的长标志符
+SYSSW_V_5.4_01	Macro identifiers shall be distinct	宏名称的含义是清晰的(因表述完整含义需要较长的字符，有些模块某些细节特性相关的宏，前罗干字符相同-如31个相同，Coverity会告警)，本身是没问题，可以变更意义不大。
+SYSSW_V_5.5_01	宏标识符与其他标识符不得重名	由于历史原因，存在结构体成员与第三方代码宏重名
+SYSSW_V_5.7_01	标记名称应是唯一的标识符。	内核或第三方代码中原有定义
+SYSSW_V_5.8_01	使用外部链接定义对象或函数的标识符应该唯一。	多个工具一起编译时有个main函数
+SYSSW_V_5.8_02	使用外部链接定义对象或函数的标识符应该唯一。	继承的对外接口中的结构成员变量和标准库或第三方库接口重名
+SYSSW_V_8.4_01	对象定义不包含一个可见的原型	引用开源代码宏引入的问题
+SYSSW_V_8.4_02	对象定义不包含一个可见的原型	定义的全局变量在coverity未扫描的代码中引用。因此不能在定义位置添加static。
+SYSSW_V_8.5_01	函数被声明了多次	函数在定义的.c中声明一次，在引用的.c中作extern外部声明。受限于放在.h中只放置autosar接口/存在互相包含的风险，所以没有通过.h声明。
+SYSSW_V_8.6_01	函数 "" 已声明，但从未定义。	由汇编代码定义实现的函数
+SYSSW_V_8.6_02	全局标识符应在且只在一处定义	在第三方库里由weak申明的函数，重新定义
+SYSSW_V_8.6_03	变量只有声明，没有定义	"变量定义在EB工具生成文件中，变量名为工具开发时固定的，文件由用户配置并生成。
+驱动中需要引用该变量/通过宏引用该变量，也就是引用EB中用户配置的参数集合。"
+SYSSW_V_8.7_01	对象 "" 具有外部链接，但仅用在了一个编译单元中。	"引用开源代码宏引入的问题, 符号由链接脚本以及系统代码使用"
+SYSSW_V_8.7_02	对象 "" 具有外部链接，但仅用在了一个编译单元中。	库中的对外接口，本编译单元没有调用。
+SYSSW_V_8.7_03	对象 "" 具有外部链接，但仅用在了一个编译单元中。	驱动中EXPORT_SYMBOL的符号，给别的模块调用
+SYSSW_V_8.7_04	对象 "" 具有外部链接，但仅用在了一个编译单元中。	文件复用在另外的编译模块单元中，在那个模块中会被别的文件调用，所以不能定义成static.
+SYSSW_V_8.7_05	对象 "" 具有外部链接，但仅用在了一个编译单元中。	"驱动中某个.c 定义一个全局变量/函数，该变量/函数在其他文件中引用，但：
+(1) 引用位置被宏包起，某些配置情况下宏才会被开启，因此这个全局变量/函数不能定义为static
+(2) 函数在OS周期任务或中断处理函数中或用户代码中调用，由用户决定，因此这个函数不能定义为static
+(3) 在eb配置回调函数时，eb生成文件中调用"
+SYSSW_V_8.9_01	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	汇编代码中使用的变量
+SYSSW_V_8.9_02	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	内核驱动框架习惯风格要求的结构和变量作为全局变量。
+SYSSW_V_8.9_03	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	较大的结构或数组可以不要求放在函数内部
+SYSSW_V_8.10_01	内联函数应该通过静态存储类声明。	内联函数需要在多个文件引用。
+SYSSW_V_9.5_01	使用指定的初始化器对数组对象执行初始化时，应显式指定数组的大小。	引用第三方库中的宏引起的，无法在自研代码中修改
+SYSSW_V_10.1_01	"Operands shall not be of an inappropriate essential type。
+典型情况: 比如 运算符 ""<<"" 或者 ""|"" 的操作数 """" 不具有基本无符号类型"	引用开源代码宏引入的问题（不适合修改外部的文件），且具体值范围合理，分析后并不会导致问题
+SYSSW_V_10.1_02	！！操作符，只适用与布尔变量	由于历史原因，需要将32位寄存器值转换为布尔值
+SYSSW_V_10.3_01	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	枚举最后作为整数写入硬件配置寄存器
+SYSSW_V_10.3_02	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	枚举需要比较范围
+SYSSW_V_10.3_03	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	由于历史原因，枚举需要转成整数传递给别的函数或结构
+SYSSW_V_10.3_04	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	从json中解析的参数转为枚举
+SYSSW_V_10.3_05	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	uin32变量位运算并移位后，将最低bit值取出作为枚举类型的返回值
+SYSSW_V_10.3_06	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	由于历史原因，整数需要转换为枚举传递给别的函数或结构
+SYSSW_V_10.4_01	左操作数 的基本类型（无符号型）与右操作数 的基本类型（带符号型）不同。	使用optee kernel开源代码中定义的宏(如TEE_SUCCESS等)的告警，不好直接修改开源代码中的宏定义.
+SYSSW_V_10.5_01	表达式的值不应(强制)转换为不适当的基本类型	由于历史原因，整数需要转成枚举传递给别的函数或结构
+SYSSW_V_10.5_02	表达式的值不应(强制)转换为不适当的基本类型	Boolean值最后作为整数写入硬件配置寄存器
+SYSSW_V_10.7_01	"如果将复合表达式用作执行常规算术转换的运算符的一个操作数，则另一个操作数不得具有更宽的基本类型
+(窄转宽)"	"时间转换函数里面，用到一些转换到tv_nsec nanoseconds which valid values are [0, 999999999], and uint32_t are enough to keep this value, some platform may define it(tv_nsec) as is long long, convert from uint32_t will not lose info."
+SYSSW_V_10.8_01	不应将复合表达式的值转换为不同的基本类型类别或较宽的基本类型。(窄转宽)	由于历史原因，整数需要转成枚举传递给别的函数或结构
+SYSSW_V_10.8_02	不应将复合表达式的值转换为不同的基本类型类别或较宽的基本类型。(窄转宽)	由于历史原因，计算需要结合浮点数
+SYSSW_V_11.1_01	不得在指向函数的指针和任何其他类型的指针之间进行转换	底层软件【典型Boot阶段的代码】往往需要特殊的函数与地址(基本类型)的转换，包括与第三方提供的结构体和接口的关联，无法完全避免转换（质量通过涉及和测试保障）
+SYSSW_V_11.2_01	指向不完整类型的指针不应转换为任何其他类型。	传递指针的接口不需要处理内容，隐藏结构内容，允许最终处理的函数进行转换
+SYSSW_V_11.2_02	指向不完整类型的指针不应转换为任何其他类型。	内核或第三方接口定义返回不完整结构指针类型，没有对外暴露完整结构
+SYSSW_V_11.4_01	不得在指向对象的指针和整数类型之间进行转换	由于历史原因，和原定的结构或函数之间要进行交互，虽然新的方式更合理
+SYSSW_V_11.4_02	不得在指向对象的指针和整数类型之间进行转换	描述设备寄存器地址的变量转换成指针来访问设备硬件寄存器， MCU中用结构描述一组寄存器。
+SYSSW_V_11.5_01	指向 void 的指针不应转换为指向对象的指针	内核结构中原有定义为void
+SYSSW_V_11.5_02	指向 void 的指针不应转换为指向对象的指针	传入的指针有多种可能性，需要根据其它参数来转换成具体的结构指针
+SYSSW_V_11.5_03	指向 void 的指针不应转换为指向对象的指针	内存分配返回void指针转换为对应的结构指针
+SYSSW_V_11.5_04	指向 void 的指针不应转换为指向对象的指针	void指针作为基础结构指针，需要转换为具体的结构指针
+SYSSW_V_11.6_01	指向 void 的指针不应转换为算术运算类型。	指针高位有特殊含义
+SYSSW_V_11.6_02	类型为 "void *" 的表达式 "" 被转换为类型 ""。	引用开源代码宏引入的问题
+SYSSW_V_11.6_03	指向 void 的指针不应转换为算术运算类型。	void指针用来保存地址，地址需要转换为整数来赋值给地址相关寄存器。
+SYSSW_V_11.6_04	指向 void 的指针不应转换为算术运算类型。	需要判断void指针指向的地址是否对齐
+SYSSW_V_11.8_01	指针所指向类型的转换不应移除任何常量或易失性属性	内核框架定义的回调函数，是const *的指针，但自已代码里要修改内容，并确认返回内核后不会再用这个指针。
+SYSSW_V_11.8_02	指针所指向类型的转换不应移除任何常量或易失性属性	"第三方框架回调函数定义的参数是const *指针，在函数实现时要第三方函数，要传这个指针，但没有定义成const, 实际上内部并不会修改指针指向内容。"
+SYSSW_V_13.6_01	sizeof 运算符的操作数不应包含具有潜在其他作用的任何表达式。	内核宏所包含的sizeof
+SYSSW_V_15.1_01	使用 "goto" 语句。	异常处理，跳转到统一的异常返回处理入口，减少重复代码和return.
+SYSSW_V_15.1_02	使用 "goto" 语句。	为了减少return和统一返后前处理， 正常路径下向下跳到正常处理退出的标签
+SYSSW_V_16.4_01	Switch 语句没有非空 default 子句。	其它情况默认代码流程已经包含，不需要做任何额外处理。
+SYSSW_V_17.1_01	不应使用 <stdarg.h> 的功能。	用于log打印记录可允许
+SYSSW_V_17.2_01	函数不得直接或间接调用自身(不得使用递归函数)	"递归用法默认不允许。
+仅当明确分析了时间复杂度(可在有限确定的时间内退出而非死循环)和空间影响度(深度对stack的影响) AND 最好是其输入数据是范围/长度是有确约束的 - 对时空的影响是预期可控的"
+SYSSW_V_18.4_01	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	按单字节或不定长的buffer指针处理
+SYSSW_V_18.4_02	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	计算结构首地址不得不用
+SYSSW_V_18.4_03	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	调用linux内核宏函数
+SYSSW_V_18.5_01	参数的声明类型包含超过两层的指针嵌套	第三方框架定义的回调函数中的申明
+SYSSW_V_20.1_01	#include 指令之前只能包含其他预处理器指令或注释。	MCU上通过宏改变include的头文件
+SYSSW_V_20.7_01	宏参数扩展成一个表达式，但该参数没有被括号括起来	引用开源代码宏引入的问题
+SYSSW_V_20.7_02	宏参数展开产生的表达式应放在括号内	内核结构中原有定义，经分析可能误报
+SYSSW_V_20.10_01	使用了预处理运算符 "#" 或者 "##" 。	deubg打印会使用##连接字符串
+SYSSW_V_20.10_02	使用了预处理运算符 "#" 或者 "##" 。	pinctrl框架惯例
+SYSSW_V_21.3_01	不应使用 <stdlib.h> 的内存分配和重新分配函数	初始化时分配内存
+SYSSW_V_21.3_02	不应使用 <stdlib.h> 的内存分配和重新分配函数	只能运行时确定的内存大小
+SYSSW_V_21.7_01	不应该调用atoi	C标准库函数
+SYSSW_V_21.8_01	不应使用库函数 abort	aarch64架构下 abort通常通过生成一个同步中止异常，终止程序运行，abort 行为确定
+SYSSW_V_22.8_01	"在调用 errno-setting-function 之前，应将 errno 的值设置为
+零。"	"第三方系统中自带的C库实现, 没有errno的功能"
+SYSSW_V_22.9_01	在调用 errno-setting-function 之后，应测试 errno 的值是否为零。	"第三方系统中自带的C库实现, 没有errno的功能"
+SYSSW_V_4.7_01	函数 "strtoul" 未提供任何其他 "errno" 是否设置的指示。应测试变量 "errno" 是否为零。	"strtoul为ATF代码中自带的C库实现, 没有errno的功能"
+SYSSW_V_VOCF_01	VOCF指标[0-5]	一个结构的很多成员变量同时初始化成相同值，或从另外一个结构变量的成员变量中复制过来
+SYSSW_V_VOCF_02	VOCF指标[0-5]	第三方代码原生宏，会有很多相同操作
+SYSSW_V_VOCF_03	VOCF指标[0-5]	"(1) MCU驱动由于需要去EB工具转化的代码里拿参数，或者使用AUTOSAR相关数据结构，导致有大量的指针/多级指针引用，包括： 在逻辑判断条件中引用、多级指针引用后赋值、调用函数时传入形参中的引用 (2) 驱动本身构造大型数据结构，集合IP所有 feature，导致多级指针的引用 (3) 为实现AUTOSAR驱动状态机，导致switch case的某个case中再次嵌套switch case，复杂度上升。 服务某个功能的函数，不宜拆分，if判断情况较多，复杂度上升。 (4) 寄存器数值的提取及处理，需要连续的位运算"
+SYSSW_V_VOCF_04	VOCF指标[0-5]	结构体的值需要根据输入条件配置成不同的参数。
+SYSSW_V_VOCF_05	VOCF指标[0-5]	已经精简的ioctl函数处理函数，无法再拆分
+SYSSW_V_VOCF_06	VOCF指标[0-5]	不展开宏，不超标
+SYSSW_V_CCM_01	CCM指标[0-15]	处理类型/情况较多，每种情况下需要进行一系列寄存器操作/子函数调用，需连续执行，导致函数行数过多。
+SYSSW_V_CCM_02	CCM指标[0-15]	该接口为加内存接口，需要检查项目的数量客观存在，无需修改
+SYSSW_V_CCM_03	CCM指标[0-15]	不展开宏，不超标
+SYSSW_V_CYCLE_01	CYCLE指标<1	只在debug的时候，手动触发的路径或Debug版本才会触发的路径
+SYSSW_V_LEVEL_01	LEVEL指标<=5	不展开宏，不超标
+SYSSW_V_STMT_01	度量值高于 HIS 度量标准策略所允许的最大值 100.00	不展开宏，不超标
+SYSSW_V_STMT_02	度量值高于 HIS 度量标准策略所允许的最大值 100.00	一些结构体的各个成员初始化占用代码行数比较多(典型是一些是init函数)，这种不增加代码复杂度，如果函数超标是因为这种情况，忽略此指标的违背。
diff --git a/package.json b/package.json
index cb33df4..27a55de 100755
--- a/package.json
+++ b/package.json
@@ -454,6 +454,11 @@
           "when": "editorHasSelection && editorTextFocus",
           "group": "1_modification@1"
         },
+        {
+          "command": "sourceseek.coverityViolation",
+          "when": "editorTextFocus",
+          "group": "1_modification@2"
+        },
         {
           "command": "sourceseek.searchSymbol",
           "when": "editorHasSelection && editorTextFocus",
@@ -479,6 +484,11 @@
           "when": "editorTextFocus",
           "group": "navigation@4"
         },
+        {
+          "submenu": "sourceseek.review.menu",
+          "when": "editorTextFocus",
+          "group": "navigation@6"
+        },
         {
           "submenu": "sourceseek.context.menu",
           "when": "editorTextFocus",
diff --git a/rules/c_rules.md b/rules/c_rules.md
new file mode 100644
index 0000000..b19090b
--- /dev/null
+++ b/rules/c_rules.md
@@ -0,0 +1,365 @@
+
+# 地平线C语言规范-基本规则
+
+|版本|修改人|评审人|修订时间|修订内容|
+|-|-|-|-|-|
+|1.0.0|凌晓峰|软件工程规范TMG-C组|2021/9/23|1.0终版|
+|2.0.0|凌晓峰|软件工程规范TMG-C组|2022/12/21|2.0终版|
+|2.1.0|凌晓峰|软件工程规范TMG-C组|2023/11/13|2.1修订版|
+
+# 1 风格
+
+## 1.1 行长
+
+### 1.1.1 代码行长不超过120，推荐不超过80, 包括空格，tab按8个空格计算
+
+## 1.2 缩进对齐
+
+### 1.2.1 新代码统一使用tab作缩进，除了历史遗留代码或第三方代码
+
+### 1.2.2 结构中成员定义类型和变量名之间用tab区隔对齐
+
+```
+/*
+ * The poor guys doing the actual heavy lifting.  All on-duty workers are
+ * either serving the manager role, on idle list or on busy hash.  For
+ * details on the locking annotation (L, I, X...), refer to workqueue.c.
+ *
+ * Only to be used in workqueue and async.
+ */
+struct worker {
+        /* on idle list while idle, on busy hash table while busy */
+        union {
+                struct list_head        entry;  /* L: while idle */
+                struct hlist_node       hentry; /* L: while busy */
+        };
+
+        struct work_struct      *current_work;  /* L: work being processed */
+        work_func_t             current_func;   /* L: current_work's fn */
+        struct pool_workqueue   *current_pwq;   /* L: current_work's pwq */
+        struct list_head        scheduled;      /* L: scheduled works */
+
+        /* 64 bytes boundary on 64bit, 32 on 32bit */
+
+        struct task_struct      *task;          /* I: worker task */
+        struct worker_pool      *pool;          /* A: the associated pool */
+                                                /* L: for rescuers */
+        struct list_head        node;           /* A: anchored at pool->workers */
+                                                /* A: runs through worker->node */
+```
+注：clang-format不支持结构中这种对齐，如果要用clang-format处理，需用/* clang-format off \*/ /\* clang-format on */包起来
+
+### 1.2.3 if, while, for, do, switch 左括号{在同一行， 右括号}和首字母对齐。
+
+### 1.2.4 switch, case, default, 右括号} 按首字母对齐
+
+```
+        switch (key->both.offset & (FUT_OFF_INODE|FUT_OFF_MMSHARED)) {
+        case FUT_OFF_INODE:
+                ihold(key->shared.inode); /* implies smp_mb(); (B) */
+                break;
+        case FUT_OFF_MMSHARED:
+                futex_get_mm(key); /* implies smp_mb(); (B) */
+                break;
+        default:
+                /*
+                 * Private futexes do not hold reference on an inode or
+                 * mm, therefore the only purpose of calling get_futex_key_refs
+                 * is because we need the barrier for the lockless waiter check.
+                 */
+                smp_mb(); /* explicit smp_mb(); (B) */
+        }
+```
+
+### 1.2.5 预处理宏指令全部从行头开始，无缩进
+
+## 1.3 分行对齐
+
+对于行长度太长需要换行的对齐
+
+### 1.3.1 换行先用tab对齐上一行前面的缩进，再用空格对齐上一行相关位置
+
+### 1.3.2 函数调用按左括号右一格对齐
+
+```
+ kthread_create_on_node(worker_thread, worker, pool->node,
+                        "kworker/%s", id_buf);
+```
+
+### 1.3.3 赋值换行按缩进一tab对齐
+
+```
+worker->task = 
+           kthread_create_on_node(worker_thread, worker, pool->node,
+                                  "kworker/%s", id_buf); 
+```
+
+### 1.3.4 条件语句换行按左括号右一对齐
+
+```
+if (worker->current_work == work &&
+    worker->current_func == work->func)
+    return worker;
+```
+
+### 1.3.5 函数定义左括号右一对齐
+
+```
+struct workqueue_struct *alloc_workqueue(const char *fmt,
+                                         unsigned int flags,
+                                         int max_active, ...)
+{
+```
+
+###   引号换行按引号右一格对齐
+
+## 1.4 空格
+
+### 1.4.1 运算符前后加空格，关键字和括号间加空格
+
+### 1.4.2 小括号左（后面，小括号右)前面不加空格
+
+```
+if (id == 0) {
+  ...
+}
+switch (mode) {
+  ...
+}
+for (i = 0; i < 10; i++) {
+ ...
+}
+```
+
+### 1.4.3 函数之间加一个空行
+
+### 1.4.4 头文件引用和全局变量之间加换行
+
+## 1.5 括号
+
+### 1.5.1 if/else如果都是单语句，可以都不加{}， 如果只有一个单行语句，都要加{}
+
+### 1.5.2 if/else代码段，如果if里面已经return了，后面就不需要写else了；
+
+### 1.5.3 左大括号{跟在行尾, 函数定义的除外, 关键字， 见样例
+
+```
+        for (i = 0; i < VIO_MP_MAX_FRAMES; i++) {
+                frame_start = (struct mp_vio_frame *)frame_array_addr[i];
+                if (!frame_start)
+                        continue;
+                first_index = frame_start->first_indx;
+
+                vfree(frame_start);
+        }
+```
+
+## 2 类型
+
+## 2.1 定义
+
+### 2.1.1 数据类型不要使用原始定义，使用uint32_t, uint16_t等明确长度的定义
+### 2.1.2 标识符的命名要清晰、明了，有明确含义，同时使用完整的单词或大家基本可以理解的缩写，避免使人产生误解。
+
+## 2.2 宏
+
+### 2.2.1 magic number必须用宏标识, 除了0和1
+### 2.2.2 用宏定义表达式时，要使用完备的括号, 以免扩展后改变了流程
+### 2.2.3 宏定义中尽量不使用return、goto、continue、break等改变程序流程的语句。
+
+## 2.3 头文件
+
+### 2.3.1 除inline函数外，头文件中适合放置接口的声明，不适合放置实现。
+
+### 2.3.2 .c/.h文件禁止包含用不到的头文件，头文件通过#ifdef防止重复包含
+
+### 2.3.3 只能通过包含头文件的方式使用其他.c提供的接口，禁止在.c中通过extern的方式使用外部函数接口、变量，禁止未声明函数直接调用函数
+
+### 2.3.4 禁止在头文件中定义变量, 只能声明全局变量
+
+### 2.3.5 头文件应向稳定的方向包含,应当让不稳定的模块依赖稳定的模块
+   自研代码中包含顺序为C标准库、C++标准库、其它库的头文件、本工程的头文件，本目录头文件
+
+### 2.3.6 头文件中链接库的头文件用\<\>，其它用\"\"包含
+
+## 2.4 常量
+
+### 2.4.1  对于数值、字符串、宏、枚举等常量的定义，建议采用全大写字母，单词之间加下划线的方式命名。
+
+## 2.5 变量
+
+### 2.5.1 全局变量尽量不要用，尤其可重入函数应避免使用共享变量，如果定义了，要有较详细的注释，包括对其功能、取值范围以及存取时注意事项等的说明。
+
+### 2.5.2 全局变量命名应增加特定前缀，如"g\_"
+### 2.5.3 禁止宽类型向窄类型的隐形转换，如int转换成short, 需要加显式的强制转换，并加注释说明丢失高位不影响
+
+## 3 函数
+
+## 3.1 命名
+
+### 3.1.1 如果函数是特定作用于一个模块、域、对象、编程形式的，可以以其作为前缀命名
+
+### 3.1.2 同一类的函数名风格保持一致，比如都是模块+动词+形容词(可选)+名词
+
+### 3.1.3 只在本文件内调用的函数应该加static修饰
+
+### 3.1.4 除了只在本文件内调用的static函数，函数名必须标识当前模块名信息，包括通过回调注册到文件外部的static函数
+
+  目的除了避免链接的冲突，对于回调函数也是便于调试，在回调函数中有可能有重名的回调函数。
+
+### 3.1.5 用正确的反义词组命名具有互斥意义的变量或相反动作的函数
+
+### 3.1.6 尽量避免名字中出现无意义数字编号，除非逻辑上的确需要编号。
+
+## 3.2 局部变量定义
+
+### 3.2.1 函数内部的局部变量尽量放在函数前面声明，如果要用，只能放到中间带有{}的block内前面声明
+
+### 3.2.2 指针变量、表示资源描述符的变量，BOOL变量声明必须赋予初值
+	例如fd推荐初始化为-1、避免局部变量未初始化，导致访问随机错误值，其他局部变量如果能够确认后续被赋值后再使用，可以不用在声明时初始化 
+
+### 3.2.3 局部变量不能与全局变量同名。
+
+### 3.2.4 不使用大的局部变量数组/buffer,以免栈溢出，建议用户态进程函数局部变量不超过8192字节
+
+### 3.2.5 禁止使用单字节命名变量，但允许定义i、j、k、m、n、v作为局部循环变量，p、q作为局部指针变量。
+
+## 3.3 入参
+
+### 3.3.1 对外函数必须作入参校验；（以模块为单位）
+
+### 3.3.2 不通过参数传结构体
+	如果结构大小不超过8字节，在函数内只读，可以直接传递，需加const并需加注释
+
+### 3.3.3 参数个数不超过8个， 传入参数在内部不被修改，用const声明
+
+### 3.3.4 第一次从外部传入的结构中获取指针时，需要检查，再传给本模块的其它函数，函数里面不用检查
+
+## 3.4 函数体
+
+### 3.4.1 函数有效行数原则上不超过70行，（如果超出70行，需要检查嵌套深度和代码最大路径）
+
+### 3.4.2 函数的圈复杂度建议不超过15
+
+### 3.4.3 简短函数建议用inline声明
+
+### 3.4.4 尽量避免使用while(1)代码段，如果一定要加，需要注释和解释循环退出条件；while里面的函数需要有阻塞函数
+
+### 3.4.5 不要使用递归函数；如果一定要使用，需要注释和解释递归退出条件；
+
+## 3.5 异常处理
+
+### 3.5.1 异常分支必须处理，现阶段无法明确的异常处理，必须加panic或ASSERT；
+
+### 3.5.2 除了错误处理，不允许使用goto语句；
+
+### 3.5.3 函数需要仔细检查是否需要有返回值，对肯定不会有异常执行路径的函数可以没有返回值，如果无返回值的函数，需要注释
+
+### 3.5.4 如果函数返回出错，应当相当于这个函数没有跑，所有资源释放或回退；
+
+### 3.5.5 异常返回时要有ASSERT或异常打印告知
+
+### 3.5.6 返回正常时，正常返回统一出口，例外情况是在函数开头判断已经打开，已经初始化等条件，可返回正常。
+
+## 3.6 返回值
+
+### 3.6.1 如果函数的名字是一个动作或者强制性的命令，那么这个函数应该返回0表示成功，负数表示错误代码整数。如果是一个判断，那么函数应该返回一个\"成功\" 布尔值，1-成功，0-失败
+
+### 3.6.2 函数返回值，如果是status类型，尽量用枚举或宏定义替代，0和1可以例外
+
+### 3.6.3 调用函数时，需要对函数返回值要做校验后，再继续运行,对于不关心返回值的场景，需要加void标识
+	防止对返回值的错误处理，没有正确的识别返回值的语义，错误的判断返回值意义，进行了错误的处理，可能导致业务流错误蔓延，或者进入错误的业务流分支；防止对业务有影响的返回值没有判断，造成访问空指针，或者继续运行失败的业务流，导致错误蔓延，或者资源未回退，导致泄露。
+
+### 3.6.4 函数返回值为指针类型，需要作有效性判断，判断方法需要确认，不一定能用NULL判断，可能需要用IS_ERR方式；
+
+###   不返回结构体，不返回局部变量结构指针
+
+## 4 语言
+
+## 4.1 分配释放
+
+### 4.1.1 内存申请前，必须对申请内存大小进行合法性校验
+
+### 4.1.2 内存分配后必须判断是否成功
+
+### 4.1.3 禁止使用realloc()函数
+
+### 4.1.4 禁止使用alloca()函数申请栈上内存
+
+### 4.1.5 如果一个函数的返回内容是内部申请的资源，必须要有对应另外的函数释放该资源，需要检查是否成对调用
+
+### 4.1.6 一个结构或其它资源的释放时需要判断引用计数, 没有要加注释说明。
+
+### 4.1.7  free后，指针变量如果为全局变量或入参结构中的指针变量，相应释放的内存变量要设成NULL，防止野指针被意外访问，导致程序崩溃
+
+## 4.2 原子性检查
+
+### 4.2.1 链表操作要加锁保护，如果不加，需要注释说明没有竞争条件
+
+### 4.2.2 可并发函数中对非局变量的非原子访问（++, --, +=, -=等），应通过互斥手段（锁或原子增减）对其加以保护,不加锁需要注释说明没有竞争条件
+
+### 4.2.3 对于全局的标志变量的先判断后改变，需要加锁，不加锁需要注释说明没有竞争条件
+
+### 4.2.4 修改全局的标志变量的某些位时，需要加锁保证原子性,不加锁要加注释说明没有竞争条件。
+
+### 4.2.5 如果某个文件中的函数都是没有并发场景的，可以在文件开头加注释，后面就不用每个地方加了。
+
+### 4.2.6 结构初始化完成之间，不能加入链表,使其它线程能访问到。
+
+
+## 4.3 注释
+
+### 4.3.1 作为库函数向外提供的接口需要在头文件申明处提供标准函数说明（功能，参数，返回值），可以通过doxgen等生成API手册,
+    见下面模板
+
+```
+/**
+ * @brief 简单描述
+ * 简单描述....
+ *
+ * 详细描述（如果有的话）简单描述和详细描述间一定要有一行空行
+ * @param[in] fileName 文件名
+ * @param[in] fileMode 文件模式，可以由以下几个模块组合而成：
+ * -r读取
+ * -w 可
+ * -a 添加
+ * -t 文本模式(不能与b联用)
+ * -b 二进制模式(不能与t联用)
+ * @return 返回文件编号
+ */
+
+int
+OpenFile(const char* fileName, const char* fileMode);
+```
+
+### 4.3.2 对于非static函数，需要提供注释介绍函数功能和重要的参数与返回值，
+    对于static函数,
+    除了很简单的函数，尽量提供注释介绍功能，参数和返回值按需注释。
+### 4.3.3 结构定义各成员，宏定义，全局变量需要注释说明用处。如果行末写不下，写在行前,
+    用C标准注释。
+
+```
+struct worker {
+        /* on idle list while idle, on busy hash table while busy */
+        union {
+                struct list_head        entry;  /* L: while idle */
+                struct hlist_node       hentry; /* L: while busy */
+        };
+```
+
+### 4.3.4 函数体内复杂的逻辑加注释说明做了什么，但不是怎么做，如果一个函数体内要加注释的地方太多，考虑拆分函数，在函数头加注释。
+   注释加在代码块前面。
+
+### 4.3.5 对于和规范或常规做法不一致的地方，加注释说明
+
+### 4.3.6 对于已知有欠缺,限制或待改进的代码，加注释说明
+
+### 4.3.7 在.c的文件头加上版权说明，对外接口的.h头加上版权说明：
+
+Copyright \[year file created\] - \[last year file modified\], Horizon
+Robotics
+
+## 4.4 第三方来源代码
+
+### 4.4.1 包括开源代码和供应商代码，代码风格上按第三方原有风格，不冲突的项，新加代码按地平线要求。
+
diff --git a/rules/c_rules_kernel.md b/rules/c_rules_kernel.md
new file mode 100644
index 0000000..90c8b64
--- /dev/null
+++ b/rules/c_rules_kernel.md
@@ -0,0 +1,103 @@
+# 地平线C语言规范-linux内核
+
+# 8 Linux内核检查项
+
+## 8.1 全局/局部资源使用
+
+### 8.1.1 Linux驱动中尽量把全局变量放到device参数变量里传递；
+
+### 8.1.2 内核态局部变量占用堆栈不超过过512字节
+
+## 8.2 锁与信号量
+
+**Mutex**
+
+### 8.2.1 确保mutex在lock和unlock之前被初始化（mutex_init）过
+
+### 8.2.2 mutex的所有操作不能出现在中断上下文中
+
+### 8.2.3 同一个mutex保护的API不能嵌套调用
+
+### 8.2.4 确保mutex的lock和unlock的操作在所有执行分支上是成对出现的, 如果一个函数里面只加锁不解锁，需要在函数前加注释说明持有什么锁
+
+### 8.2.5 确保没有拿了mutex未释放返回用户空间的情况
+
+### 8.2.6 上层应用通过ioctl/read/write等系统调用方式调用到，如果关键区内有休眠或可能等待较久的硬件操作，用mutex_lock_interruptible，其它情况都用mutex_lock.
+
+### 8.2.7 使用mutex_lock_interruptible，必须判断返回值，如果被信号打断，必须返回EAGAIN，让信号处理得于执行，同时HAL层库或应用需要判断系统调用的返回值，重新调用。
+
+**Spinlock**
+
+### 8.2.8 确保spinlock在lock和unlock之前被初始化（spin_lock_init）过
+
+### 8.2.9 不推荐使用spin_lock_irq和spin_unlock_irq，如果一定要使用，需要加注释说明保护区的中断上下文属性
+
+### 8.2.10 如果一个自旋锁需要同时出现在中断上半部和进程上下文中，在进程上下文中只能使用spin_lock_irqsave和spin_unlock_irqrestore，在中断上下文中可以使用spin_lock/spin_unlock
+
+### 8.2.11 如果一个自旋锁只会出现在中断上半部，在中断不可重入的情况下使用spin_lock/spin_unlock，在中断可重入的情况下使用spin_lock_irqsave和spin_unlock_irqrestore
+
+### 8.2.12 如果一个自旋锁只会出现在进程上下文中，尽量使用spin_lock/spin_unlock，避免关中断降低实时性，增加重要中断的响应时间
+
+### 8.2.13 如果一个自旋锁需要同时出现在进程上下文和软中断中，尽量使用spin_lock_bh/spin_unlock_bh
+
+### 8.2.14 同一个spinlock保护的API不能嵌套调用
+
+### 8.2.15 确保spinlock的lock和unlock的操作在所有执行分支上是成对出现的, 如果一个函数里面只加锁不解锁，需要在函数前加注释说明持有什么锁
+
+### 8.2.16 spinlock代码段中不能休眠和主动调度（如：sleep，schedule，mutex_lock等）
+
+### 8.2.17 spinlock中间代码正常路径不要加打印（pr_debug除外），异常分支可以加打印，正常路径如果一定要加，需要注释；
+
+## 8.3 原子性检查
+
+### 8.3.1 设备驱动在一段代码中修改多个设备寄存器时需要锁保护，保证一致性，不加锁需要注释说明
+
+### 8.3.2 设备驱动修改某个设备寄存器的某些位时，是先读到CPU寄存器，修改后再写回，这种情况需要锁保证原子性, 不加锁要加注释说明, 如果存在异构核访问的,需要硬件锁。
+
+### 8.3.3 修改某个设备寄存器时,需要在拿锁的情况下进行设备有效性判断，防止并发的上下文中关闭时钟，关闭硬件等操作。
+
+### 8.3.4 在关闭某个设备的时钟，或使能寄存器时，需要在拿锁的情况下设置状态变量，阻止后续或并发上下文的访问。
+
+### 8.3.5 驱动中并发上下文除了应用的多进程多线程访问，还需考虑中断上半部，下半部，内核线程之间对全局变量以及全局结构衍生的指针指向的变量访问的原子性。
+
+### 8.3.6 驱动中需考虑同一个进程的多线程调用，如果不支持，需在内核入口处就加上互斥，如果支持，需考虑共同一个fd的关联结构并发访问的互斥。
+
+## 8.4 中断
+
+### 8.4.1 上半部中无动态内存申请函数调用
+
+### 8.4.2 无除了error用的log信息输出（调试版本中也可以加入pr_debug作为调试log）
+
+### 8.4.3 无可以被抢占调度或者睡眠的函数（如：sleep，schedule，mutex_lock等）的调用
+
+### 8.4.4 中断上半程序尽量简短，大部分中断处理流程需要在下半部运行
+
+### 8.4.5 中断上半部不能使用无限循环，或者较多次数的循环
+
+## 8.5 分配与释放
+
+### 8.5.1 kmalloc中参数GFP_KERNEL改成GFP_ATOMIC，除非在init过程中调用；
+
+### 8.5.2 gpio_request, request_irq等内核申请资源的API，建议使用devm_gpio_request, devm_request_irq等带devm的对应API，这样在相应module退出时，会自动释放相应资源；
+
+### 8.5.3 超过32K不建议使用kmalloc，可用vmalloc, 如果需要大块的物理连续内存，需要单独考虑,用ION等 建议在设备驱动中使用devm_xxxx开头函数，devm_kmalloc等, 自动释放
+
+### 8.5.4 kfree或其它封装的释放内存函数，指针变量如果为全局变量或入参结构的中指针变量，该指针变量要设成NULL，防止野指针被意外访问，导致程序崩溃
+
+### 8.5.5 驱动中使用的内存尽量在初始化时一次性申请，驱动卸载时的释放，减少在运行过程中的动态申请和释放，如果在驱动初始化时不能明确，就在设备被打开时一次性分配，或由应用调用专门的初始化ioctl接口传递所需的内存参数，如果必须使用动态内存申请和释放，需要加注释解释理由
+
+### 8.5.6 为防止资源泄漏，工作队列有初始化调用，同时必须有对应的cancel/destroy调用，类似的资源使用方式也是如此；
+
+### 8.5.7 资源申请/创建型接口，无论是返回值而还是出参，都需要检查申请到的资源的有效性
+
+### 8.5.8 注意kstrdup, kstrndup, kmemdup, kmemdup_nul等隐性内存分配函数的使用，要检查对应释放
+
+## 8.6 退出处理
+
+### 8.6.1 驱动Close回调函数中需要释放打开实例分配的所有资源，全局共享的资源需要在最后一个实例关闭时释放
+
+### 8.6.2 驱动Close回调函数中释放资源前需要等待实例相关的内核线程, tasklet, worker停止工作，DMA操作结束，固件停止工作，中断屏蔽,  最后关闭时钟。
+
+### 8.6.3 内核模块卸载时释放所有资源，关闭中断。
+
+### 8.6.4 如果硬件IP支持FUSA功能，驱动最后一个应用实例关闭时需关闭硬件Fusa功能。
diff --git a/rules/common_rules.md b/rules/common_rules.md
new file mode 100644
index 0000000..5b8d6df
--- /dev/null
+++ b/rules/common_rules.md
@@ -0,0 +1,86 @@
+# C/C++ 编码规则和最佳实践
+
+## 1. 命名规范
+
+- **变量命名**: 使用有意义的名称，避免单字母命名（除非是明确的局部临时变量，如循环索引）
+- **函数命名**: 使用动词或动词短语，表明函数的作用
+- **常量命名**: 全部大写，单词间用下划线分隔
+- **类型命名**: 使用首字母大写的驼峰式命名法
+- **宏命名**: 全部大写，单词间用下划线分隔
+
+## 2. 格式化规则
+
+- **缩进**: 使用4个空格或1个制表符（项目内保持一致）
+- **行长度**: 每行不超过80/100个字符
+- **括号风格**: 左花括号与函数/控制语句在同一行，右花括号独占一行
+- **空格**: 操作符两边添加空格，函数名与左括号之间不加空格
+- **代码块**: 即使是单行语句，也应使用花括号
+
+## 3. 注释规则
+
+- **文件头注释**: 包含文件名、作者、创建日期、版权信息等
+- **函数注释**: 描述函数功能、参数、返回值、副作用等
+- **复杂逻辑注释**: 对于复杂的逻辑，需要添加注释解释原理
+- **TODO注释**: 使用统一格式标记待完成的工作
+- **避免过时注释**: 修改代码时同步更新注释
+
+## 4. 安全性规则
+
+- **边界检查**: 总是进行数组边界检查
+- **输入验证**: 验证所有外部输入
+- **资源管理**: 使用RAII或类似技术确保资源正确释放
+- **避免危险函数**: 不使用 gets()、strcpy()等不安全函数
+- **空指针检查**: 在解引用前检查指针是否为NULL
+
+## 5. 性能考虑
+
+- **避免深拷贝**: 对大型对象使用引用或指针传递
+- **适当内联**: 短小且频繁调用的函数考虑内联
+- **减少动态内存分配**: 尽量使用栈内存或对象池
+- **减少系统调用**: 批处理I/O操作
+- **异常使用**: 只用于真正的异常情况，不用于正常流程控制
+
+## 6. 错误处理
+
+- **一致性**: 使用一致的错误处理策略（返回码或异常）
+- **错误检查**: 检查所有可能的错误条件
+- **资源清理**: 出错时确保资源被正确释放
+- **错误信息**: 提供有用的错误信息
+- **错误传播**: 在适当的层次处理错误
+
+## 7. 可维护性
+
+- **函数长度**: 每个函数应尽量短小，通常不超过50行
+- **函数职责**: 每个函数只做一件事
+- **复杂度控制**: 控制循环嵌套深度和条件复杂度
+- **避免硬编码**: 使用常量或配置替代硬编码值
+- **模块化**: 相关功能组织到同一模块
+
+## 8. C++特有规则
+
+- **资源管理**: 使用智能指针管理资源
+- **RAII原则**: 利用构造函数/析构函数管理资源
+- **避免多重继承**: 谨慎使用多重继承
+- **虚析构函数**: 基类应有虚析构函数
+- **避免异常滥用**: 异常只用于真正的异常情况
+- **使用STL**: 优先使用标准库而非自定义实现
+- **const正确性**: 正确使用const修饰符
+
+## 9. 嵌入式系统特有规则
+
+- **避免动态内存**: 尽量避免使用动态内存分配
+- **中断处理**: 中断处理函数尽量简短
+- **位操作**: 正确使用位操作处理寄存器
+- **避免递归**: 避免或严格限制递归深度
+- **任务优先级**: 明确定义任务优先级
+
+## 10. 代码审查要点
+
+- **功能正确性**: 代码是否正确实现了预期功能
+- **健壮性**: 是否处理了所有边界条件和错误情况
+- **可读性**: 代码是否容易理解
+- **可测试性**: 代码是否易于测试
+- **安全性**: 是否存在安全隐患
+- **性能**: 是否有明显的性能问题
+- **可维护性**: 代码结构是否清晰，易于维护
+
diff --git a/rules/log_rules.md b/rules/log_rules.md
new file mode 100644
index 0000000..850e4d3
--- /dev/null
+++ b/rules/log_rules.md
@@ -0,0 +1,267 @@
+# **地平线日志规范**
+
+# 公共规范
+## 关于级别
+
+* fatal 致命错误，导致系统崩溃或应用崩溃
+* error 发生错误的消息，部分功能将会异常，最终产品发布也会打开
+* warning 非预期的路径，功能可能受影响，也可能不受影响，最终产品发布也会打开
+* info 重要节点的关键提示信息，也不能过多，系统测试，集成测试时打开，最终产品视具体产品特性而定，目标是尽量能打开，所以在添加时也要慎重。
+* debug(trace/verbose) 调试信息，个人调试，单元测试时打开,最终产品可以不生成代码，也可以动态能打开，根据性能的要求而定。
+* 对于下面各领域log的API中，info和debug可能并不区分，而是warning以下再分很多级别，这时info就是指warning以下最高一级，表示最重要的正常信息。
+
+## 默认级别
+有不同的默认级别设置:
+
+* 是否编译生成打印的语句
+* 是否输出到日志的内存缓冲区中
+* 是否输出到文件中
+* 是否在标准输出设备上输出
+
+## 基本原则
+
+1. 添加相关打印信息时，一次性把需要的重要变量都展示出来，避免后期重新再加，也作为review的一个检查点。
+2. 对于特定子系统，如一个库，一个应用，通过统一的接口，统一前缀信息， 比如模块名，文件名，行号，函数名，cpu号，进程号，时戳。也要注意前缀信息不要过长。
+3. 对于特定子系统，尽量使用能够动态根据模块，文件，函数行号开关的日志接口。对于可能输出到串口的日志调试，要考虑对整个系统性能和时序的影响。
+4. 评估性能的时候，需要考虑日志的影响，不使用串口输出。进入集成测试阶段，一般不再利用串口输出日志，以免受时序影响，和最终产品运行时序不一致。
+5. 在系统稳定性相关测试需要在串口输出特定日志的，要设定串口输出级别，一般只输出error, warn级别。
+6. 如果某个子系统支持动态配置，模块开发者需要思考准备好特定问题需要的log配置项，问题解决时可以快速提供。
+7. 循环内不输出日志，特殊情况需评审，加上注释说明。
+8. 临时日志不合入主线。
+9. 日志输出信息需要考虑是否有敏感内容，需要防破解的代码是否增加了破解概率，需要防攻击的代码是否泄露了变量地址等。
+
+## 日志存放位置
+#### 在嵌入式端的默认的log存放位置：
+#### 内核日志存放位置
+/userdata/log/kernel/
+每个文件的转存大小ROTATESIZE=2048K，也就是单个kernel log文件最大2M，超过2M后将转存为带时间戳的文件名，如1970-01-01-08-00-01.kmsg。并会转存到/log/kernel/xxxx文件夹下，xxxx文件夹的名字为时间戳。如/log/kernel/1970-01-01-08-01-58文件夹
+#### 用户态日志存放位置
+/userdata/log/usr/
+每个文件的转存大小ROTATESIZE=2048K，也就是单个用户层 log文件最大2M，超过2M后将转存为带时间戳的文件名，如1970-01-01-08-00-01.umsg。并会转存到/log/usr/xxxx文件夹下，xxxx文件夹的名字为时间戳。如/log/usr/1970-01-01-08-01-58文件夹
+#### 应用崩溃dump文件存放位置
+/userdata/log/coredump， 文件格式为core-执行程序名-pid-运行时的时间戳
+
+# 特定规范
+## 1.linux内核
+### 接口使用
+
+* 在驱动中推荐使用如下接口dev_err, dev_warn, dev_info, dev_dbg
+* 没有设备结构的场合使用如下接口pr_error, pr_warn, pr_info, pr_debug
+* 对于重要信息必须打印，可直接使用printk，无法用loglevel关闭
+* 对于一旦出错可能重复非常多的打印，用如下接口printk_once 只打印一次或printk_ratelimited 限制每秒打印次数
+
+默认每5秒最多打印10次
+可以通过
+/proc/sys/kernel/printk_ratelimit
+/proc/sys/kernel/printk_ratelimit_burst
+来设置
+调用这两个需要自已在前在放上级别和pr_fmt：
+printk_ratelimited(KERN_ERR pr_fmt("end_request: %s error, dev %s, ") , xxxx, xxxx)
+
+### 关于格式设置
+在文件开头用 pr_fmt用来定义输出格式， 可加统一前缀：
+define pr_fmt(fmt) "ipu: " fmt
+调试可加入函数名：
+define pr_fmt(fmt) "hobot-xxx: %s: " fmt, __func__
+
+### 动态开关
+pr_debug可以由宏控制是否编译进去
+kernel CONFIG_DYNAMIC_DEBUG, 可以使能动态开关
+kernel/Documentation/admin-guide/dynamic-debug-howto.rst
+e.g.
+echo "file bif_base.c +p" >/sys/kernel/debug/dynamic_debug/control
+echo "func bifbase_init +p" >/sys/kernel/debug/dynamic_debug/control
+启动时打开，在uboot中设置kernel 命令行
+dyndbg="file drivers/mmc/host/* +p"
+目前默认打开 CONFIG_DYNAMIC_DEBUG， 某个文件需要及致性能优化性时，可以关掉，不编译进去。
+
+### 死机日志
+/userdata/log/pstore
+
+## 2.底层库
+### 接口
+
+代码路径位于：hbre/liblog/include/logging.h
+/* debug level */
+pr_debug(fmt, ...);
+/* info level */
+pr_info(fmt, ...);
+/* warn level */
+pr_warn(fmt, ...);
+/* error level */
+pr_error(fmt. ...);
+
+### 输出格式设置
+
+各个模块在输出log时，可能需要打印自己的模块名字，此时可以通过定义SUBSYS_NAME宏，来传入名字即可。比如，下文中示例部分的log输出，[camera]中，camera即模块的名字定义，可以在Makefile中通过-DSUBSYS_NAME=camera来传入。
+
+### 输出方式选择
+
+* Console方式
+
+这种方式是最常用的，通过串口进行输出，使用这种方式也是最简单的，直接在代码中包含头文件hbre/liblog/include/logging.h就可以了。
+
+* ALOG方式
+
+使用ALOG方式，利用安卓的日志系统，Log不会输出到终端来，使用这种方式，有三个步骤：
+1）包含头文件hbre/liblog/include/logging.h；
+2）打开ALOG_SUPPORT宏；
+3）链接libalog.so库；
+
+后两步可以在Makefile中指定，给一个示例：
+
+```
+LOG_SUPPORT = -DALOG_SUPPORT
+ LOG_LIB = -L liblog_path -llog
+/* liblog_path为libalog.so库文件的存放路径 */
+
+CFLAGS += $(LOG_SUPPORT)
+LDFLAGS += $(LOG_LIB)
+```
+
+### Log Level选择
+/* output log by console */
+ #define CONSOLE_DEBUG_LEVEL 14
+#define CONSOLE_INFO_LEVEL 13
+#define CONSOLE_WARNING_LEVEL 12
+#define CONSOLE_ERROR_LEVEL 11
+
+#define ALOG_DEBUG_LEVEL 4
+#define ALOG_INFO_LEVEL 3
+#define ALOG_WARNING_LEVEL 2
+#define ALOG_ERROR_LEVEL 1
+
+修改环境变量，来设置Log Level值，其中各个值如上图所示，
+#loglevel_value为设定值
+ export
+LOGLEVEL=loglevel_value
+
+当Log Level设定不在1 ~ 4, 11 ~ 14之间时，会默认选择成11的值。
+Log Level 设为1-4 时，并且HAL库ALOG_SUPPORT打开时，会通过libalog输出，可通过logcat工具获取。
+Log Level 设为11-14 时，通过console 用默认printf输出。
+
+### Logcat使用
+当ALOG_SUPPORT打开时，接口转为安卓的log系统，可使用logcat工具来获取log
+
+[adb] logcat [<option>] ... [<filter-spec>] ...
+[options]命令包括如下选项:
+-s 设置过滤器，例如指定 '*:s'
+-f <filename> 输出到文件，默认情况是标准输出。
+-r [<kbytes>] Rotate log every kbytes. (16 if unspecified). Requires -f
+-n <count> Sets max number of rotated logs to <count>, default 4
+-v <format> 设置log的打印格式, <format> 是下面的一种:
+brief process tag thread raw time threadtime long
+-c 清除所有log并退出
+-d 得到所有log并退出 (不阻塞)
+-g 得到环形缓冲区的大小并退出
+-b <buffer> 请求不同的环形缓冲区 ('main', 'system', 'radio', 'events',默认为"-b main -b system")
+-B 输出log到二进制中。
+过滤器的格式是一个这样的串：
+<tag>[:priority]
+其中 <tag> 表示log的component， tag (或者使用 * 表示所有) ， priority 从低到高如下所示:
+V Verbose
+D Debug
+I Info
+W Warn
+E Error
+F Fatal
+S Silent
+
+## 3.安卓应用
+### 接口使用
+android.util.Log
+Log.v(),
+Log.d(),
+Log.i(),
+Log.w(),
+Log.e()
+
+例：
+```
+private static final String TAG = "MyActivity";
+Log.v(TAG, "index=" + i);
+```
+ 
+
+
+## 4.嵌入式应用
+
+* 发布版本，禁止开启Debug及以下级别的日志；【强制】
+* FATAL 级别的日志应该保留足够的上下文函数调用关系信息；【推荐】
+* 统一使用平台提供的公司级的 日志服务/库 提供的接口 完成日志输出；【推荐】
+* 考虑日志流量，例如，当需要将日志存储在spi-flash这种慢速硬件上时，或者在慢速网络设备上时，需要考虑限制每秒日志输出的大小。【推荐】
+* 使用统一的日志输出路径，建议在/userdata/log/app下【推荐】
+* 日志文件名的格式相对统一
+后缀名.log 【强制】
+{Timstamp}_{进程名}_{进程ID}_{Number}.log 【推荐】
+Timestamp 的格式：YYYYMMDD-HHMMSS
+
+Number：是指的次数，一旦时间同步服务没有开启时，只依托Timestamp不便于定位
+
+* 日志输出的格式化；【强制】
+
+%L%m%d %H:%M:%S.%e %s:%#]<%n>(%t) %v
+
+| 符号 | 含义 |
+| --- | --- |
+|  |  |
+|%L|日志等级：F：Fatal / E: Error / W:Warning / I：Info / D: Debug / T: Trace|
+|%t|线程ID|
+|%m|月份: 08|
+|%d|日期：01-31|
+|%H|小时：00-23|
+|%M|分钟：00-59|
+|%S|秒：00-59|
+|%e|毫秒：000-999|
+|%s|文件名|
+|%#|行号|
+|%n|自定义的tag / module|
+|%v|日志正文|
+
+### 接口使用
+glog
+https://github.com/google/glog#readme
+
+## 5.PC端应用
+同嵌入式应用
+
+## 6.Web前端
+主要分埋点和监控
+### 埋点：
+{
+uuid: 'xxxxx',
+url: 'https://xxx',
+eventType：'pv',// 分类：pv/页面停留/曝光/交互事件/逻辑事件
+eventValue: 'xxxx',
+eventTime: '2018-09-10 10:20:30', // 上传用户本地时间字符串
+extra: 'xxx' //额外信息，可以是对象进行json字符串化的结果
+}
+
+## 7.云端/后端
+
+### 日志级别：
+#### info日志
+入日志系统，用来追查问题（fn表示当次请求的返回结果，非负数表示成功，fn>0表示返回多条结果，fn=0表示正常处理，fn<0 表示处理失败）。
+
+格式的范例如下：
+{ "timestamp": "2018-09-10 10:20:30", "threadid":1220,"Modulename":"mainmodule1" ，"trackid":"0xa12318990101", “fn”: 10, "TM":510,"Parse":10, "Module1":150,"Module2":180,"Module3":160,"Rtime":10, "tm":140,"parset":10, "module1":"30", "module2":40,"module3":50, "rtime":10}
+
+#### Warning日志：
+warning日志，本地记录，记录异常问题、流程无法继续，问题跟进的重要依据， 会添加报警短信、邮件、微信通知。格式的范例如下：
+{time: "2018-09-10 10:20:30", threadid:1220,"picid":0xa12318990101, "message"：“this is error, you should notice that”}
+
+### 日志信息必选项
+日志包含唯一traceId用于定位多个服务调用
+
+### 输出
+日志输出到标准输出，结合日志采集agent 采集到日志服务
+
+### 推荐logging选型
+golang：logrus
+
+# 待后续解决问题
+
+* 关于应用和库输出log的统一
+* HAL层log动态配置开关问题
+* 是否使用spdlog作为公司的标准log库 
+
diff --git a/rules/log_rules_amend.md b/rules/log_rules_amend.md
new file mode 100644
index 0000000..d749cc2
--- /dev/null
+++ b/rules/log_rules_amend.md
@@ -0,0 +1,132 @@
+# 系统软件日志规范补充
+
+## 规范更新日志
+
+| 更新日期 | 更新人 | 更新说明 |
+| --- | --- | --- |
+| 2023.2.2 | 王思远，王云乾，凌晓峰，余明 | 初版 |
+
+## 目的
+
+统一日志和注释规范，并将注释信息能够解析到表格，表达出log代表的状态信息，方便开发同学分析问题，同时测试人员也能根据日志和提取的注释信息来更直观的确认测试结果
+
+## 日志规范
+
+此日志规范是在地平线日志规范的基础上做了些补充，主要介绍日志的打印相关注释的格式规范
+
+### 日志格式
+
+主要针对warn和error级别的日志，格式如下：
+subject：predicate + object, extra
+
+举例：vio_sif cpu_id thread_id file line：transfer failed，channel=1
+
+#### 2.1.1 细则
+
+1. **subject（主语）**：子系统、模块名字，关键路径标志（cpu id、线程id、文件名、行号）
+2. **predicate + object（谓语+宾语）**：做了什么，产生了什么结果，例如可以定义为rx，tx，init，函数执行流程出现的异常状态...
+3. **extra**：额外的定制log，各个子模块可以独立去封装定义，例如下面
+    1. **count**：计数：针对不同需求的计数，如 txcnt：1 rxcnt：2
+    2. **channel**：通道： 当前所处的数据通道
+4. **格式中符号代表意义**
+    1. ： 主语描述完毕后使用：与后面的输出语句进行分割
+    2. ，作为不同属性打印状态的分割
+    3. 当遇到表征同一属性需要增加多个log的时候，可用空格隔开记录
+
+#### 2.1.2 表达用语标准
+
+1. 口语化表达，玩具化表达，不应出现在release版本中
+2. error, fail等单词，不应出现在正常日志语句中
+3. 不应使用感叹号，会导致客户恐慌
+4. 不应中英文标点混用，可能会导致显示乱码
+
+### 2.2 注释格式
+
+1. 针对error，warn级别的错误情况，在要打印日志的前一行进行注释，格式如下
+    1. `/*! <explain> 异常现象：可能的原因补充 */`
+        1. 对要打印的日志进行解释说明，表达打印的日志代表什么信息，出现了什么现象，如果明确产生原因，加入出现这个现象的原因的描述
+2. **具体形式**
+    1. 最终各个模块要调用统一定义好的接口（带有关键信息）去打印log
+        1. **内核层**
+            1. 基于dev_xxx，pr_xxx的命名，对其fmt进行整改，增加关键路径的相关信息
+            2. 按照debug和release版本来去区分不同的关键路径信息封装
+                1. **debug**：cpu id、线程id、文件名、行数名、行号都存在
+                2. **release**：cpu id、线程id
+                3. 对应release和debug版本的区分通config配置变量传入 
+                    1. **J5**：区分debug和release是在build中根据传入的编译参数判断对.config进行sed -i xxx追加
+                        1. J6的debug和release是否还是使用这种方式
+
+```C
+#if defined(CONFIG_DEBUG_LOG_FMT)
+#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
+#define dev_critical_path_fmt(fmt) "[C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
+#endif
+#if defined(CONFIG_RELEASE_LOG_FMT)
+#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d] " fmt, get_cpu(), current->pid
+#define dev_critical_path_fmt(fmt) "[C:%d P:%d] " fmt, get_cpu(), current->pid
+#endif
+```
+
+        2. **应用层**
+            1. 按照原来的方式正常使用ALOG的相关接口，针对关键信息我们在ALOG内部函数中做了封装，对于使用log接口的开发者是无感的，还是延续以前的使用方式
+                1. 相应的关键信息我们按照debug和release版本来去区分输出不同的信息
+                    1. **debug**：cpu id、线程id、文件名、行数名、行号都存在
+                    2. **release**：cpuid、线程id
+    2. **实际效果**
+        1. **内核层**
+            1. **注意**：当前大家在写内核驱动代码的时候
+                1. 优先关注黄色的部分即可，给每条warn和err级别的log增加注释
+                2. 关于fmt的宏定义暂定如下，还需要跟build的开发同事确认，后续确定好形式再和大家同步
+
+```C
+#if defined(CONFIG_DEBUG_LOG_FMT)
+#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
+#define dev_critical_path_fmt(fmt) "[C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
+#endif
+#if defined(CONFIG_RELEASE_LOG_FMT)
+#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d] " fmt, get_cpu(), current->pid
+#define dev_critical_path_fmt(fmt) "[C:%d P:%d] " fmt, get_cpu(), current->pid
+#endif
+
+int a_func（int a）{
+    ......
+    /*! <explain> ABRT_7B_ADDR_NOACK：slave address not acknowledged (7bit mode)*/
+    dev_err(dev, "transfer failed, abort_source=%d\n", ABRT_7B_ADDR_NOACK);
+    ......
+}
+```
+
+        2. **应用层**
+        3. 
+            保持原来的log接口使用方式即可
+            **注意**：但要给每条warn、err级别的日志加上注释如上黄色标注的一样
+
+### 2.3 错误日志添加规则
+
+1. 如果在一个函数中有多个相同的返回值的异常路径，每个地方都需要加log输出区别
+2. 如果在一个循环中异常返回，需要加log输出循环相关信息
+3. 驱动中返回到用户态的最上层函数，由中断调用的第一层函数，内核线程调用的第一层函数，这三类函数中的错误路径需要加log，用户态库的返回应用的最上层函数错误路径需要加log
+4. 当上面的c条件不足于判断函数最底层异常返回路径的，需要在下层函数各个地方加log
+5. 引起走到错误路径的变量需要打印出来，其它有助于判断的错误路径的变量也要打印出来
+6. IP寄存器读写失败的情况，在fusa做检查时候打印出来详细的寄存器地址信息，方便低概率问题排查
+
+#### 范例
+
+1. **在一个函数中有多个相同的返回值的异常路径，每个地方都需要加log输出区别**，如下面的-EINVAL、-ENOMEM相关的异常返回路径
+
+2. 如果在一个循环中异常返回，需要加log输出循环相关信息
+3. 驱动中返回到用户态的最上层函数，由中断处理第一层函数，内核线程调用的第一层函数，这三类函数中的错误路径需要加log，用户态库的返回应用的最上层函数错误路径需要加log.
+4. 当上面的c条件不足于判断函数最底层异常返回路径的，需要在下层函数各个地方加log.
+     例：A->B, B调了C和D, A是最上层函数，有打印，但B中C， D的返回值相同的，B中就需要加打印才能知道是C返回失败还D失败。
+
+### 2.4 warn补充
+
+* 代表warn信息的日志如果不需要处理，不要加入到代码中.
+* warn 信息比较敏感，如果是不需要处理的warn信息（也就是说非问题类的warn 信息），我们是不希望看到的，需要明确这一类的打印，在编写log的时候，不需要处理的warn信息不要加入到代码中.
+   举例：
+        如果retry正常运行场景中如果经常碰到, 比如别人在用，要等一下，不需要加warn，如果正常场景不应该有的retry,可能硬件已经有异常，需要加入warn做预警。
+
+### 2.5 打印频率
+
+* 循环中不能打印正常日志，异常情况打印，需要跳出循环或者限制次数或频率.
+  
\ No newline at end of file
diff --git a/rules/python_rules.md b/rules/python_rules.md
new file mode 100644
index 0000000..3a74621
--- /dev/null
+++ b/rules/python_rules.md
@@ -0,0 +1,205 @@
+# Python代码规范指南
+|   **版本**   |   **修改人** |   **评审人**    |  **修订时间**  |  **修订内容**  |
+| :------: | :------: | :---------: | :--------: | :--------: |
+|   V1.00  |  屈树谦  | - | 2021-10-11 | 评审版发布 | 
+
+目录
+=================
+- [python代码规范指南](#python代码规范指南)
+- [目录](#目录)
+- [正文](#正文)
+- [0 阅前说明](#0-阅前说明)
+- [1 Code Lay-out](#1-Code Lay-out)
+  - [1.1 规范](#11-规范)
+  - [1.2 检查工具](#12-检查工具)
+  - [1.3 Auto-format 工具](#13-Auto-format 工具)
+  - [1.4 执行说明](#14-执行说明)
+- [2 String Quotes](#2-String Quotes)
+  - [2.1 规范](#21-规范)
+  - [2.2 检查工具](#22-检查工具)
+  - [2.3 Auto-format 工具](#23-Auto-format 工具)
+  - [2.4 执行说明](#24-执行说明)
+- [3 Whitespace in Expressions and Statements](#3-Whitespace in Expressions and Statements)
+  - [3.1 规范](#31-规范)
+  - [3.2 检查工具](#32-检查工具)
+  - [3.3 Auto-format 工具](#33-Auto-format 工具)
+  - [3.4 执行说明](#34-执行说明)
+- [4 When to Use Trailing Commas](#4-When to Use Trailing Commas)
+  - [4.1 规范](#41-规范)
+  - [4.2 检查工具](#42-检查工具)
+  - [4.3 Auto-format 工具](#43-Auto-format 工具)
+  - [4.4 执行说明](#44-执行说明)
+- [5 Comments](#5-Comments)
+  - [5.1 规范](#51-规范)
+  - [5.2 检查工具](#52-检查工具)
+  - [5.3 Auto-format 工具](#53-Auto-format 工具)
+  - [5.4 执行说明](#54-执行说明)
+- [6 Naming Conventions](#6-Naming Conventions)
+  - [6.1 规范](#61-规范)
+  - [6.2 检查工具](#62-检查工具)
+  - [6.3 Auto-format 工具](#63-Auto-format 工具)
+  - [6.4 执行说明](#64-执行说明)
+- [7 Programming Recommendations](#7-Programming Recommendations)
+  - [7.1 规范](#71-规范)
+  - [7.2 检查工具](#72-检查工具)
+  - [7.3 Auto-format 工具](#73-Auto-format 工具)
+  - [7.4 执行说明](#74-执行说明)
+- [8 工具说明](#8-工具说明)
+  - [8.1 检查工具](#82-检查工具)
+  - [8.2 Auto-format 工具](#83-Auto-format 工具)
+
+
+正文
+=================
+
+# 0 阅前说明
+- python版本为3.6.x
+- 一级条目参考 [pep8](https://www.python.org/dev/peps/pep-0008)。
+- 每个条目上都从规范，检查工具，auto-format 工具和执行说明等四个方面进行阐述。
+- 每个规范都由多个子条目组成，一般第一条为基础规范，后面为对基础规范的补充说明。
+
+# 1 Code Lay-out
+## 1.1 规范
+### 1.1.1 遵从PEP8的规定，详情参见: [pep8 code-lay-out](https://www.python.org/dev/peps/pep-0008/#code-lay-out)
+### 1.1.2 imports路径：除了当前目录可以采用相对路径（.），其他均使用绝对路径
+
+【说明】
+
+绝对路径相对于相对路径的可读性更好。事实上一般规范都会建议使用绝对路径。
+
+### 1.2 检查工具
+
+flake8
+
+### 1.3 Auto-format 工具
+
+- black
+- isort
+
+### 1.4 执行说明
+
+与自动化工具冲突时可适当ignore冲突的错误码，如W503（Line break occurred before a binary operator）。
+
+# 2 String Quotes
+## 2.1 规范
+### 2.1.1 优先使用双引号（" 和 """）
+### 2.2 检查工具
+### 2.3 Auto-format 工具
+
+black
+
+### 2.4 执行说明
+
+自动化工具（black）会自动转换单引号为双引号，如果觉得实在不妥，例如单引号/双引号有不同含义，可以skip掉（--skip-string-normalization）。
+
+# 3 Whitespace in Expressions and Statements
+## 3.1 规范
+### 3.1.1 遵从PEP8的规定，详情参见：[pep8 whitespace-in-expressions-and-statements](https://www.python.org/dev/peps/pep-0008/#whitespace-in-expressions-and-statements)
+### 3.2 检查工具
+
+flake8
+
+### 3.3 Auto-format 工具
+
+black
+
+### 3.4 执行说明
+
+与自动化工具冲突时可适当ignore冲突的错误码，如E203（whitespace before ':'）。
+
+# 4 When to Use Trailing Commas
+## 4.1 规范
+### 4.1.1 遵从black的规定，详情参见：[black training-commas](https://black.readthedocs.io/en/stable/the_black_code_style/current_style.html#trailing-commas)
+### 4.2 检查工具
+### 4.3 Auto-format 工具
+
+black
+
+### 4.4 执行说明
+
+# 5 Comments
+## 5.1 规范
+### 5.1.1 遵从PEP8的规定，详情参见：[pep8 comments](https://www.python.org/dev/peps/pep-0008/#comments)
+### 5.1.2 docstring（格式补充）
+
+- sections
+ - 使用缩进而非换行来区分不同的section。
+ - section heads请使用Args:、Returns: (or Yields: for generators)、Raises:、Attributes:(for class public attributes)和Examples:。
+- type annotation
+ - 如果args中已经添加了type annotation，则无需在docstring中说明；否则需要在描述args进行说明。
+- 示例参见：[google style docstrings](https://sphinxcontrib-napoleon.readthedocs.io/en/latest/example_google.html)
+
+### 5.1.3 docstring（可读性说明）
+
+- 希望能够达到只看docstring就知道怎样使用的效果，不要草草了事;
+- 伴随着代码变更及时更新，如某个逻辑变更或者新增参数;
+- 必要的时候添加example进行说明。
+
+### 5.2 检查工具
+
+- flake8
+- pydocstyle
+
+### 5.3 Auto-format 工具
+
+black
+
+### 5.4 执行说明
+
+文档详细程度很多时候与项目强相关，请根据具体情况执行。
+
+# 6 Naming Conventions
+## 6.1 规范
+### 6.1.1 遵从PEP8的规定，详情参见：[pep8 naming-convertions](https://www.python.org/dev/peps/pep-0008/#naming-conventions)
+### 6.1.2 尽量不要在接口上使用非业界通用或者共识的缩写。
+
+### 6.2 检查工具
+
+pep8-naming
+
+### 6.3 Auto-format 工具
+### 6.4 执行说明
+
+命名规范很多时候与常用语，语义或者上下文相关，对于检查工具的使用只是推荐并不强制。
+
+# 7 Programming Recommendations
+## 7.1 规范
+### 7.1.1 遵从PEP8的规定，详情参见：[pep8 programming-recommendations](https://www.python.org/dev/peps/pep-0008/#programming-recommendations)
+### 7.1.2 针对容易出现的bug/design问题补充flake8-bugbear规定，参见[flake8-bugbear](https://github.com/PyCQA/flake8-bugbear)
+### 7.1.3 list/set/dict的使用遵从flake-comprehensions的规定，参见[flake8-comprehensions](https://github.com/adamchainz/flake8-comprehensions)
+### 7.1.4 Function Annotations：Public的method/function必须添加type hints
+### 7.1.4 Threading：不要依赖build-in types的原子性
+
+【说明】
+
+一般情况下，python的build-in类型操作原子性与实现有关，不能保证一定是原子的。详情说明参见[theading](https://google.github.io/styleguide/pyguide.html#218-threading)
+
+### 7.1.5 \*args, \*\*kwargs：谨慎使用
+
+【说明】
+
+\*args, \*\*kwargs功能比较强大，随便使用可能导致接口的可维护性差，用户使用时可读性也不好。建议使用具体含义的args。
+
+
+### 7.2 检查工具
+
+- flake8
+- flake8-bugbear
+- flake-comprehensions
+- mypy
+
+### 7.3 Auto-format 工具
+### 7.4 执行说明
+
+优秀的Python编写实践是偏软性的，尤其是检查工具不能涉及的范围，很多规范也没有相应的检查工具能够检查出来。只能在代码review阶段进行检查。
+
+
+# 8 工具说明
+## 8.1 检查工具
+
+主要采用flake8系列，包括flake8, flake8-bugbear和flake8-comprehensions，以及mypy，均为社区常用的检查工具，成熟度好。
+
+## 8.2 Auto-format 工具
+
+采用black和isort，black社区活跃度高，运行速度快且限制更加严格，一致性好。迁移的示例说明见[replace yapf with black](https://github.com/PyTorchLightning/pytorch-lightning/pull/7783)。
+
diff --git a/rules/shell_rules.md b/rules/shell_rules.md
new file mode 100644
index 0000000..6cd2c3f
--- /dev/null
+++ b/rules/shell_rules.md
@@ -0,0 +1,157 @@
+# Shell脚本规范
+
+**版本** 1.0.0
+
+# 1 文件头
+
+## 1.1 每个文件的开头必须对文件进行描述
+对于产品中包含的脚本使用，加上版权和功能描述
+对于内部使用的工具类脚本，加上作者和日期，便于使用者和作者探讨
+```
+#!/bin/bash
+#Copyright: Horizon Robotic
+#Function: This scripts is used to generate boot.img
+```
+
+## 1.2 注意脚本运行的范围，正确的指定解释器和路径
+是在地平线芯片上运行还是在主机上运行，在文件头上指定所用的解释路径，非标准shell语法，需要指定特定的解释器。
+```
+#!/usr/bin/env sh
+#!sh
+#!/usr/bin/bash
+```
+
+## 1.3 通过 set 命令添加脚本调试
+如果报错，脚本直接退出，不继续执行，对于管道错误也可以直接退出,未定义变量的。
+调试时可以加上set -x，显示执行命令
+```
+#!/bin/bash
+
+set -eu
+set -o pipefail
+```
+
+# 2 风格
+
+## 2.1 1. 1. 每行最大长度为80个字，长的字符串可以用特殊方式
+
+```
+#DO use 'here document's
+cat <<END
+I am an exceptionally long
+string.
+END
+
+# Embedded newlines are ok too
+long_string="I am an exceptionally
+long string."
+
+```
+
+## 2.2 缩进采用tab, 按4个空格计算
+
+
+## 2.3 判断与循环
+将 ; do , ; then 和 while , for , if 放在同一行，else单独一行, 分号后面空一格
+
+```
+if condition ; then
+    command
+else
+    command
+fi
+for name in $(cat file); do
+    echo ${name}
+done
+```
+
+## 2.4 多路判断
+case 如果命令只有一行，命令可以和分号放在一行，其他情况下;单独一行，条件对齐case，命令缩进
+
+```
+case "${expression}" in
+a)
+    command
+    ;;
+b)
+    command
+    ;;
+*)
+    command
+    ;;
+esac
+```
+
+## 2.5 函数
+函数名小写和下划线分割单词, 函数名和() 没有空格, 函数定义加function前缀
+```
+function my_func() {
+    echo "hello"
+}
+```
+
+## 2.6 常量和环境变量名采用大写和下划线分割单词
+
+## 2.7 变量名大写还是小写在一个文件中统一, 引用时加{}
+```
+echo "${var}"
+```
+
+## 2.8 文件名使用小写, 用.sh作后缀
+ 作为直接给用户用的命令形的脚本，可以没有后缀，作为库被包含的文件，必须有后缀。命令默认加上可执行权限。
+
+# 3.语法建议
+
+## 3.1 局部变量加local标识
+
+## 3.2 判断语句使用 [[，不使用test 和 [
+
+在 [[ 和 ]] 之间不会有路径名称扩展或单词分割发生，所以使用[[ … ]]能够减少错误。而且 [[ … ]]允许正则表达式匹配，而[ … ]不允许
+
+## 3.3 测试字符串 使用-z 或 -n 测试，不使用 [[ "${var}" = "" ]]
+
+## 3.4 所有的错误信息导向stderr
+
+```
+if [[ ${result} -ne 0 ]];then
+    echo "wrong command" >&2
+fi
+```
+
+## 3.5 使用 && 或 || 简化判断语句，语义更加清晰
+
+## 3.6 不使用 $? -eq 0 检查命令退出状态，直接使用if cmd; 来判断。
+
+## 3.7 使用$(cmd)来执行命令，不使用
+
+## 3.8 包含变量，命令结果替换，空格或shell特殊字符的字符串始终加引号
+```
+# "quote command substitutions"
+flag="$(some_command and its args "$@" 'quoted separately')"
+
+# "quote variables"
+echo "${flag}"
+```
+
+## 3.9 脚本中路径变量，最后面不要使用/结尾
+
+## 3.10 需要判断函数返回值
+
+# 4.检查工具
+
+* **shfmt 用于风格检查**
+
+shfmt -d myshell.sh
+这个规范里的格式符合shfmt的默认风格，shfmt也可以用开关设定不一样的风格。
+
+snap install shfmt
+
+* **shellcheck 用于语法检查**
+
+shellcheck myshell.sh
+
+apt install shellcheck
+# 参考
+https://google.github.io/styleguide/shellguide.html
+
+
